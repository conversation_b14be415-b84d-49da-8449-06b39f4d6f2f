using UnityEngine;
using BlastingDesign.Events;
using BlastingDesign.Events.Core;
using BlastingDesign.Utils;

namespace BlastingDesign.Events.Tests
{
    /// <summary>
    /// EventSystemManager测试类
    /// 验证EventSystemManager.EventBus属性是否正常工作
    /// </summary>
    public class EventSystemManagerTest : MonoBehaviour
    {
        [Header("测试设置")]
        [SerializeField] private bool runTestOnStart = true;
        [SerializeField] private bool enableDetailedLogging = true;

        private void Start()
        {
            if (runTestOnStart)
            {
                // 延迟测试，确保EventSystemManager已初始化
                Invoke(nameof(RunTest), 0.5f);
            }
        }

        [ContextMenu("运行EventSystemManager测试")]
        public void RunTest()
        {
            Logging.LogInfo("EventSystemManagerTest", "开始EventSystemManager测试");

            // 测试1: 检查EventSystemManager实例
            TestEventSystemManagerInstance();

            // 测试2: 检查EventBus属性
            TestEventBusProperty();

            // 测试3: 测试事件发布和订阅
            TestEventPublishSubscribe();

            Logging.LogInfo("EventSystemManagerTest", "EventSystemManager测试完成");
        }

        private void TestEventSystemManagerInstance()
        {
            Logging.LogInfo("EventSystemManagerTest", "测试1: 检查EventSystemManager实例");

            var manager = BlastingDesign.Events.EventSystemManager.Instance;
            if (manager != null)
            {
                Logging.LogInfo("EventSystemManagerTest", "✅ EventSystemManager实例存在");
                
                if (enableDetailedLogging)
                {
                    Logging.LogInfo("EventSystemManagerTest", $"Manager类型: {manager.GetType().FullName}");
                    Logging.LogInfo("EventSystemManagerTest", $"GameObject名称: {manager.gameObject.name}");
                }
            }
            else
            {
                Logging.LogError("EventSystemManagerTest", "❌ EventSystemManager实例不存在");
            }
        }

        private void TestEventBusProperty()
        {
            Logging.LogInfo("EventSystemManagerTest", "测试2: 检查EventBus属性");

            var manager = BlastingDesign.Events.EventSystemManager.Instance;
            if (manager != null)
            {
                try
                {
                    var eventBus = manager.EventBus;
                    if (eventBus != null)
                    {
                        Logging.LogInfo("EventSystemManagerTest", "✅ EventBus属性正常");
                        
                        if (enableDetailedLogging)
                        {
                            Logging.LogInfo("EventSystemManagerTest", $"EventBus类型: {eventBus.GetType().FullName}");
                            Logging.LogInfo("EventSystemManagerTest", $"EventBus接口: {typeof(IEventBus).FullName}");
                        }
                    }
                    else
                    {
                        Logging.LogError("EventSystemManagerTest", "❌ EventBus属性为null");
                    }
                }
                catch (System.Exception ex)
                {
                    Logging.LogError("EventSystemManagerTest", $"❌ EventBus属性访问异常: {ex.Message}");
                }
            }
            else
            {
                Logging.LogError("EventSystemManagerTest", "❌ 无法测试EventBus属性，EventSystemManager实例不存在");
            }
        }

        private void TestEventPublishSubscribe()
        {
            Logging.LogInfo("EventSystemManagerTest", "测试3: 测试事件发布和订阅");

            var manager = BlastingDesign.Events.EventSystemManager.Instance;
            if (manager?.EventBus != null)
            {
                try
                {
                    bool eventReceived = false;
                    string receivedMessage = "";

                    // 订阅测试事件
                    var subscription = manager.EventBus.Subscribe<StatusMessageEvent>(evt =>
                    {
                        eventReceived = true;
                        receivedMessage = evt.Message;
                        
                        if (enableDetailedLogging)
                        {
                            Logging.LogInfo("EventSystemManagerTest", $"收到测试事件: {evt.Message}");
                        }
                    });

                    // 发布测试事件
                    var testEvent = new StatusMessageEvent("EventSystemManager测试事件");
                    manager.EventBus.Publish(testEvent);

                    // 检查结果
                    if (eventReceived && receivedMessage == "EventSystemManager测试事件")
                    {
                        Logging.LogInfo("EventSystemManagerTest", "✅ 事件发布和订阅测试通过");
                    }
                    else
                    {
                        Logging.LogError("EventSystemManagerTest", $"❌ 事件发布和订阅测试失败 - 接收: {eventReceived}, 消息: {receivedMessage}");
                    }

                    // 清理订阅
                    subscription?.Dispose();
                }
                catch (System.Exception ex)
                {
                    Logging.LogError("EventSystemManagerTest", $"❌ 事件发布和订阅测试异常: {ex.Message}");
                }
            }
            else
            {
                Logging.LogError("EventSystemManagerTest", "❌ 无法测试事件发布和订阅，EventBus不可用");
            }
        }

        [ContextMenu("测试UIElementBase事件系统集成")]
        public void TestUIElementBaseIntegration()
        {
            Logging.LogInfo("EventSystemManagerTest", "测试UIElementBase事件系统集成");

            // 创建一个测试用的UIElementBase子类实例
            var testElement = new TestUIElement();
            
            try
            {
                testElement.Initialize();
                testElement.TestEventSystem();
                Logging.LogInfo("EventSystemManagerTest", "✅ UIElementBase事件系统集成测试通过");
            }
            catch (System.Exception ex)
            {
                Logging.LogError("EventSystemManagerTest", $"❌ UIElementBase事件系统集成测试失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 测试用的UIElementBase子类
        /// </summary>
        private class TestUIElement : BlastingDesign.UI.Core.UIElementBase
        {
            protected override string TemplatePath => "Test/TestElement";

            protected override void CacheUIElements()
            {
                // 测试用，不需要实际缓存UI元素
            }

            protected override void SetupEventListeners()
            {
                // 测试用，不需要实际设置事件监听器
            }

            protected override void RemoveEventListeners()
            {
                // 测试用，不需要实际移除事件监听器
            }

            public void TestEventSystem()
            {
                // 测试发布事件
                PublishEvent(new StatusMessageEvent("UIElementBase测试事件"));
                
                // 测试兼容性发布
                PublishCompatEvent("StatusMessage", "UIElementBase兼容性测试事件");
                
                Logging.LogInfo("TestUIElement", "事件系统测试完成");
            }
        }
    }
}
