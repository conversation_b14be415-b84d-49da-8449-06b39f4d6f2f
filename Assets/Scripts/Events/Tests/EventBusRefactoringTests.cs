using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using NUnit.Framework;
using BlastingDesign.Events.Core;

namespace BlastingDesign.Events.Tests
{
    /// <summary>
    /// 测试EventBus重构后的功能
    /// </summary>
    [TestFixture]
    public class EventBusRefactoringTests
    {
        private EventBus _eventBus;
        private TestEvent _receivedEvent;
        private int _eventCount;

        [SetUp]
        public void SetUp()
        {
            _eventBus = new EventBus(debugMode: true);
            _receivedEvent = null;
            _eventCount = 0;
        }

        [TearDown]
        public void TearDown()
        {
            _eventBus?.Dispose();
        }

        [Test]
        public void Subscribe_And_Publish_Should_Work()
        {
            // Arrange
            var testEvent = new TestEvent("Test Message");
            
            // Act
            _eventBus.Subscribe<TestEvent>(evt => {
                _receivedEvent = evt;
                _eventCount++;
            });
            
            _eventBus.Publish(testEvent);
            
            // Assert
            Assert.IsNotNull(_receivedEvent);
            Assert.AreEqual(testEvent.Message, _receivedEvent.Message);
            Assert.AreEqual(1, _eventCount);
        }

        [Test]
        public void Subscribe_With_Filter_Should_Work()
        {
            // Arrange
            var testEvent1 = new TestEvent("Include");
            var testEvent2 = new TestEvent("Exclude");
            
            // Act
            _eventBus.Subscribe<TestEvent>(evt => {
                _receivedEvent = evt;
                _eventCount++;
            }, evt => evt.Message.Contains("Include"));
            
            _eventBus.Publish(testEvent1);
            _eventBus.Publish(testEvent2);
            
            // Assert
            Assert.IsNotNull(_receivedEvent);
            Assert.AreEqual("Include", _receivedEvent.Message);
            Assert.AreEqual(1, _eventCount); // 只有一个事件被处理
        }

        [Test]
        public void Subscribe_With_Priority_Should_Work()
        {
            // Arrange
            var processOrder = new List<int>();
            var testEvent = new TestEvent("Test");
            
            // Act - 按优先级订阅（高优先级先执行）
            _eventBus.Subscribe<TestEvent>(evt => processOrder.Add(1), priority: 1);
            _eventBus.Subscribe<TestEvent>(evt => processOrder.Add(3), priority: 3);
            _eventBus.Subscribe<TestEvent>(evt => processOrder.Add(2), priority: 2);
            
            _eventBus.Publish(testEvent);
            
            // Assert
            Assert.AreEqual(3, processOrder.Count);
            Assert.AreEqual(3, processOrder[0]); // 最高优先级
            Assert.AreEqual(2, processOrder[1]);
            Assert.AreEqual(1, processOrder[2]); // 最低优先级
        }

        [Test]
        public void Unsubscribe_Should_Work()
        {
            // Arrange
            var testEvent = new TestEvent("Test");
            Action<TestEvent> handler = evt => {
                _receivedEvent = evt;
                _eventCount++;
            };
            
            // Act
            _eventBus.Subscribe(handler);
            _eventBus.Publish(testEvent);
            
            _eventBus.Unsubscribe(handler);
            _eventBus.Publish(testEvent);
            
            // Assert
            Assert.AreEqual(1, _eventCount); // 只有第一次发布被处理
        }

        [Test]
        public void GetSubscriberCount_Should_Work()
        {
            // Arrange & Act
            Assert.AreEqual(0, _eventBus.GetSubscriberCount<TestEvent>());
            
            _eventBus.Subscribe<TestEvent>(evt => {});
            Assert.AreEqual(1, _eventBus.GetSubscriberCount<TestEvent>());
            
            _eventBus.Subscribe<TestEvent>(evt => {});
            Assert.AreEqual(2, _eventBus.GetSubscriberCount<TestEvent>());
            
            _eventBus.ClearSubscriptions<TestEvent>();
            Assert.AreEqual(0, _eventBus.GetSubscriberCount<TestEvent>());
        }

        [Test]
        public void HasSubscribers_Should_Work()
        {
            // Arrange & Act
            Assert.IsFalse(_eventBus.HasSubscribers<TestEvent>());
            
            _eventBus.Subscribe<TestEvent>(evt => {});
            Assert.IsTrue(_eventBus.HasSubscribers<TestEvent>());
            
            _eventBus.ClearSubscriptions<TestEvent>();
            Assert.IsFalse(_eventBus.HasSubscribers<TestEvent>());
        }

        [Test]
        public async Task PublishAsync_Should_Work()
        {
            // Arrange
            var testEvent = new TestEvent("Async Test");
            
            // Act
            _eventBus.Subscribe<TestEvent>(evt => {
                _receivedEvent = evt;
                _eventCount++;
            });
            
            await _eventBus.PublishAsync(testEvent);
            
            // Assert
            Assert.IsNotNull(_receivedEvent);
            Assert.AreEqual(testEvent.Message, _receivedEvent.Message);
            Assert.AreEqual(1, _eventCount);
        }

        [Test]
        public void ErrorRecoveryStrategy_Should_Work()
        {
            // Arrange
            var testEvent = new TestEvent("Error Test");
            var errorRecoveryStrategy = new DefaultRetryRecoveryStrategy<TestEvent>(maxRetries: 2);
            
            // Act
            _eventBus.RegisterErrorRecoveryStrategy(errorRecoveryStrategy);
            
            _eventBus.Subscribe<TestEvent>(evt => {
                _eventCount++;
                throw new InvalidOperationException("Test error");
            });
            
            _eventBus.Publish(testEvent);
            
            // Assert
            var errorStats = _eventBus.GetErrorStatistics();
            Assert.IsNotNull(errorStats);
            Assert.Greater(errorStats.TotalErrors, 0);
        }

        [Test]
        public void Dispose_Should_Clean_Resources()
        {
            // Arrange
            _eventBus.Subscribe<TestEvent>(evt => {});
            Assert.IsTrue(_eventBus.HasSubscribers<TestEvent>());
            
            // Act
            _eventBus.Dispose();
            
            // Assert
            Assert.Throws<ObjectDisposedException>(() => _eventBus.Subscribe<TestEvent>(evt => {}));
        }
    }

    /// <summary>
    /// 测试事件类
    /// </summary>
    public class TestEvent : EventBase
    {
        public string Message { get; }
        
        public TestEvent(string message) : base("TestSource")
        {
            Message = message;
        }
    }
}