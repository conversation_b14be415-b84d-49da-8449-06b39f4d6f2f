using System.Collections.Generic;
using UnityEngine;
using BlastingDesign.Events.Core;
using BlastingDesign.Events.Compatibility;
using BlastingDesign.UI.Components;
using BlastingDesign.Utils;

namespace BlastingDesign.Events.Tests
{
    /// <summary>
    /// Toolbar迁移测试类
    /// 验证Toolbar组件迁移后的功能完整性
    /// </summary>
    public class ToolbarMigrationTest : MonoBehaviour
    {
        [Header("测试设置")]
        [SerializeField] private bool enableDetailedLogging = true;
        [SerializeField] private bool testOldEventSystem = true;
        [SerializeField] private bool testNewEventSystem = true;
        [SerializeField] private float testInterval = 2f;

        private List<IEventSubscription> testSubscriptions;
        private int testEventCount = 0;
        private int receivedEventCount = 0;
        private bool testInProgress = false;

        // 测试结果
        private Dictionary<string, bool> testResults;

        private void Start()
        {
            testSubscriptions = new List<IEventSubscription>();
            testResults = new Dictionary<string, bool>();

            // 延迟启动测试，确保所有系统都已初始化
            Invoke(nameof(StartTests), 1f);
        }

        private void StartTests()
        {
            Logging.LogInfo("ToolbarMigrationTest", "开始Toolbar迁移测试");

            if (testNewEventSystem)
            {
                TestNewEventSystem();
            }

            if (testOldEventSystem)
            {
                TestOldEventSystemCompatibility();
            }

            // 开始自动化测试
            InvokeRepeating(nameof(RunAutomatedTests), testInterval, testInterval);
        }

        #region 新事件系统测试

        private void TestNewEventSystem()
        {
            Logging.LogInfo("ToolbarMigrationTest", "测试新事件系统");

            try
            {
                // 订阅工具按钮点击事件
                var toolButtonSub = UIEventSystemCompat.Subscribe<ToolButtonClickedEvent>(OnToolButtonClicked);
                if (toolButtonSub != null) testSubscriptions.Add(toolButtonSub);

                // 订阅工具选择事件
                var toolSelectedSub = UIEventSystemCompat.Subscribe<ToolSelectedEvent>(OnToolSelected);
                if (toolSelectedSub != null) testSubscriptions.Add(toolSelectedSub);

                // 订阅工具状态变更事件
                var toolStateSub = UIEventSystemCompat.Subscribe<ToolStateChangedEvent>(OnToolStateChanged);
                if (toolStateSub != null) testSubscriptions.Add(toolStateSub);

                // 订阅工具组切换事件
                var toolGroupSub = UIEventSystemCompat.Subscribe<ToolGroupSwitchedEvent>(OnToolGroupSwitched);
                if (toolGroupSub != null) testSubscriptions.Add(toolGroupSub);

                // 订阅状态消息事件
                var statusSub = UIEventSystemCompat.Subscribe<StatusMessageEvent>(OnStatusMessage);
                if (statusSub != null) testSubscriptions.Add(statusSub);

                testResults["NewEventSystemSubscription"] = testSubscriptions.Count > 0;
                Logging.LogInfo("ToolbarMigrationTest", $"新事件系统订阅完成，共订阅 {testSubscriptions.Count} 个事件");
            }
            catch (System.Exception ex)
            {
                testResults["NewEventSystemSubscription"] = false;
                Logging.LogError("ToolbarMigrationTest", $"新事件系统测试失败: {ex.Message}");
            }
        }

        private void OnToolButtonClicked(ToolButtonClickedEvent evt)
        {
            receivedEventCount++;
            testResults["ToolButtonClickedEvent"] = true;

            if (enableDetailedLogging)
            {
                Logging.LogInfo("ToolbarMigrationTest",
                    $"收到工具按钮点击事件: {evt.ButtonName} ({evt.DisplayName}) - " +
                    $"Toggle: {evt.IsToggle}, Active: {evt.IsActive}");
            }
        }

        private void OnToolSelected(ToolSelectedEvent evt)
        {
            receivedEventCount++;
            testResults["ToolSelectedEvent"] = true;

            if (enableDetailedLogging)
            {
                Logging.LogInfo("ToolbarMigrationTest", $"收到工具选择事件: {evt.ToolName}");
            }
        }

        private void OnToolStateChanged(ToolStateChangedEvent evt)
        {
            receivedEventCount++;
            testResults["ToolStateChangedEvent"] = true;

            if (enableDetailedLogging)
            {
                Logging.LogInfo("ToolbarMigrationTest",
                    $"收到工具状态变更事件: {evt.ToolName} - {evt.PreviousState} -> {evt.CurrentState}");
            }
        }

        private void OnToolGroupSwitched(ToolGroupSwitchedEvent evt)
        {
            receivedEventCount++;
            testResults["ToolGroupSwitchedEvent"] = true;

            if (enableDetailedLogging)
            {
                Logging.LogInfo("ToolbarMigrationTest",
                    $"收到工具组切换事件: {evt.GroupName} - {evt.PreviousToolName} -> {evt.CurrentToolName}");
            }
        }

        private void OnStatusMessage(StatusMessageEvent evt)
        {
            receivedEventCount++;
            testResults["StatusMessageEvent"] = true;

            if (enableDetailedLogging)
            {
                Logging.LogInfo("ToolbarMigrationTest", $"收到状态消息事件: {evt.Message}");
            }
        }

        #endregion

        #region 旧事件系统兼容性测试

        private void TestOldEventSystemCompatibility()
        {
            Logging.LogInfo("ToolbarMigrationTest", "测试旧事件系统兼容性");

            try
            {
                // 测试兼容性API
                UIEventSystemCompat.TriggerToolSelected("test-tool");
                UIEventSystemCompat.TriggerStatusMessage("测试状态消息");

                testResults["OldEventSystemCompatibility"] = true;
                Logging.LogInfo("ToolbarMigrationTest", "旧事件系统兼容性测试通过");
            }
            catch (System.Exception ex)
            {
                testResults["OldEventSystemCompatibility"] = false;
                Logging.LogError("ToolbarMigrationTest", $"旧事件系统兼容性测试失败: {ex.Message}");
            }
        }

        #endregion

        #region 自动化测试

        private void RunAutomatedTests()
        {
            if (testInProgress) return;

            testInProgress = true;
            testEventCount++;

            // 模拟工具选择
            string[] testTools = { "select", "move", "rotate", "scale", "delete" };
            string selectedTool = testTools[testEventCount % testTools.Length];

            try
            {
                // 使用兼容性API触发事件
                UIEventSystemCompat.TriggerToolSelected(selectedTool);
                UIEventSystemCompat.TriggerStatusMessage($"自动测试 #{testEventCount}: 选择工具 {selectedTool}");

                testResults[$"AutoTest_{testEventCount}"] = true;

                if (enableDetailedLogging)
                {
                    Logging.LogInfo("ToolbarMigrationTest", $"自动测试 #{testEventCount}: 触发工具选择 {selectedTool}");
                }
            }
            catch (System.Exception ex)
            {
                testResults[$"AutoTest_{testEventCount}"] = false;
                Logging.LogError("ToolbarMigrationTest", $"自动测试 #{testEventCount} 失败: {ex.Message}");
            }

            testInProgress = false;

            // 每10次测试输出一次统计
            if (testEventCount % 10 == 0)
            {
                PrintTestStatistics();
            }
        }

        #endregion

        #region 测试结果和统计

        private void PrintTestStatistics()
        {
            int passedTests = 0;
            int totalTests = testResults.Count;

            foreach (var result in testResults)
            {
                if (result.Value) passedTests++;
            }

            float successRate = totalTests > 0 ? (float)passedTests / totalTests * 100f : 0f;

            Logging.LogInfo("ToolbarMigrationTest",
                $"测试统计 - 总测试: {totalTests}, 通过: {passedTests}, 成功率: {successRate:F1}%");
            Logging.LogInfo("ToolbarMigrationTest",
                $"事件统计 - 发送: {testEventCount}, 接收: {receivedEventCount}");

            // 详细结果
            if (enableDetailedLogging)
            {
                foreach (var result in testResults)
                {
                    string status = result.Value ? "✅" : "❌";
                    Logging.LogInfo("ToolbarMigrationTest", $"{status} {result.Key}");
                }
            }
        }

        [ContextMenu("手动运行测试")]
        public void ManualTest()
        {
            Logging.LogInfo("ToolbarMigrationTest", "手动运行测试");

            // 测试各种工具选择
            string[] tools = { "select", "move", "rotate", "scale" };
            foreach (string tool in tools)
            {
                UIEventSystemCompat.TriggerToolSelected(tool);
                UIEventSystemCompat.TriggerStatusMessage($"手动测试: 选择工具 {tool}");
            }

            PrintTestStatistics();
        }

        [ContextMenu("重置测试")]
        public void ResetTest()
        {
            testResults.Clear();
            testEventCount = 0;
            receivedEventCount = 0;
            Logging.LogInfo("ToolbarMigrationTest", "测试已重置");
        }

        #endregion

        private void OnDestroy()
        {
            // 清理订阅
            if (testSubscriptions != null)
            {
                foreach (var subscription in testSubscriptions)
                {
                    subscription?.Dispose();
                }
                testSubscriptions.Clear();
            }

            // 输出最终统计
            PrintTestStatistics();
            Logging.LogInfo("ToolbarMigrationTest", "Toolbar迁移测试结束");
        }
    }
}
