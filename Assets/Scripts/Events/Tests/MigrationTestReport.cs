using System;
using System.Collections.Generic;
using System.Text;
using UnityEngine;
using BlastingDesign.Events.Migration;
using BlastingDesign.Utils;

namespace BlastingDesign.Events.Tests
{
    /// <summary>
    /// 迁移测试报告生成器
    /// </summary>
    public static class MigrationTestReport
    {
        /// <summary>
        /// 生成Toolbar迁移测试报告
        /// </summary>
        /// <returns>测试报告内容</returns>
        public static string GenerateToolbarMigrationReport()
        {
            var report = new StringBuilder();
            var timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
            
            report.AppendLine("# Toolbar组件迁移测试报告");
            report.AppendLine($"**生成时间**: {timestamp}");
            report.AppendLine();
            
            // 基础信息
            report.AppendLine("## 测试概述");
            report.AppendLine("本报告记录了Toolbar组件从旧事件系统迁移到新事件系统的测试结果。");
            report.AppendLine();
            
            // 迁移验证
            report.AppendLine("## 迁移验证");
            var validationResult = ValidateToolbarMigration();
            report.AppendLine($"**验证状态**: {(validationResult.IsValid ? "✅ 通过" : "❌ 失败")}");
            
            if (validationResult.Errors.Count > 0)
            {
                report.AppendLine("**错误列表**:");
                foreach (var error in validationResult.Errors)
                {
                    report.AppendLine($"- ❌ {error}");
                }
            }
            
            if (validationResult.Warnings.Count > 0)
            {
                report.AppendLine("**警告列表**:");
                foreach (var warning in validationResult.Warnings)
                {
                    report.AppendLine($"- ⚠️ {warning}");
                }
            }
            report.AppendLine();
            
            // 功能测试
            report.AppendLine("## 功能测试");
            var functionTests = GetToolbarFunctionTests();
            
            int passedTests = 0;
            foreach (var test in functionTests)
            {
                string status = test.Value ? "✅ 通过" : "❌ 失败";
                report.AppendLine($"- **{test.Key}**: {status}");
                if (test.Value) passedTests++;
            }
            
            float successRate = functionTests.Count > 0 ? (float)passedTests / functionTests.Count * 100f : 0f;
            report.AppendLine($"**成功率**: {successRate:F1}% ({passedTests}/{functionTests.Count})");
            report.AppendLine();
            
            // 性能测试
            report.AppendLine("## 性能测试");
            var performanceData = GetPerformanceTestData();
            foreach (var data in performanceData)
            {
                report.AppendLine($"- **{data.Key}**: {data.Value}");
            }
            report.AppendLine();
            
            // 兼容性测试
            report.AppendLine("## 兼容性测试");
            var compatibilityTests = GetCompatibilityTests();
            foreach (var test in compatibilityTests)
            {
                string status = test.Value ? "✅ 兼容" : "❌ 不兼容";
                report.AppendLine($"- **{test.Key}**: {status}");
            }
            report.AppendLine();
            
            // 建议和总结
            report.AppendLine("## 建议和总结");
            if (validationResult.IsValid && successRate >= 90f)
            {
                report.AppendLine("✅ **迁移成功**: Toolbar组件已成功迁移到新事件系统，功能完整，性能良好。");
                report.AppendLine("📋 **后续步骤**: 可以继续进行其他组件的迁移工作。");
            }
            else if (successRate >= 70f)
            {
                report.AppendLine("⚠️ **部分成功**: Toolbar组件迁移基本完成，但存在一些问题需要解决。");
                report.AppendLine("🔧 **建议**: 修复上述错误和警告后再进行下一步迁移。");
            }
            else
            {
                report.AppendLine("❌ **迁移失败**: Toolbar组件迁移存在严重问题，需要重新检查和修复。");
                report.AppendLine("🚨 **紧急**: 请立即修复关键问题，暂停其他组件迁移。");
            }
            
            report.AppendLine();
            report.AppendLine("---");
            report.AppendLine($"*报告生成于 {timestamp} by BlastingDesign Event Migration System*");
            
            return report.ToString();
        }

        /// <summary>
        /// 验证Toolbar迁移状态
        /// </summary>
        private static MigrationValidationResult ValidateToolbarMigration()
        {
            var result = new MigrationValidationResult();
            
            try
            {
                // 检查EventMigrationHelper是否存在
                var helper = FindObjectOfType<EventMigrationHelper>();
                if (helper != null)
                {
                    result = helper.ValidateMigration();
                }
                else
                {
                    result.Errors.Add("EventMigrationHelper未找到");
                    result.IsValid = false;
                }
                
                // 检查Toolbar组件是否存在
                var toolbar = FindObjectOfType<BlastingDesign.UI.Components.Toolbar>();
                if (toolbar == null)
                {
                    result.Errors.Add("Toolbar组件未找到");
                }
                else
                {
                    result.ValidatedEventTypes.Add("Toolbar组件已找到");
                }
                
                // 检查StatusBar组件是否存在
                var statusBar = FindObjectOfType<BlastingDesign.UI.Components.StatusBar>();
                if (statusBar == null)
                {
                    result.Warnings.Add("StatusBar组件未找到，可能影响事件订阅测试");
                }
                else
                {
                    result.ValidatedEventTypes.Add("StatusBar组件已找到");
                }
            }
            catch (Exception ex)
            {
                result.Errors.Add($"验证过程异常: {ex.Message}");
                result.IsValid = false;
            }
            
            return result;
        }

        /// <summary>
        /// 获取Toolbar功能测试结果
        /// </summary>
        private static Dictionary<string, bool> GetToolbarFunctionTests()
        {
            var tests = new Dictionary<string, bool>();
            
            try
            {
                // 检查事件类型是否存在
                tests["ToolButtonClickedEvent类型"] = typeof(ToolButtonClickedEvent) != null;
                tests["ToolSelectedEvent类型"] = typeof(ToolSelectedEvent) != null;
                tests["ToolStateChangedEvent类型"] = typeof(ToolStateChangedEvent) != null;
                tests["ToolGroupSwitchedEvent类型"] = typeof(ToolGroupSwitchedEvent) != null;
                
                // 检查兼容性API
                tests["UIEventSystemCompat可用"] = typeof(BlastingDesign.Events.Compatibility.UIEventSystemCompat) != null;
                
                // 检查EventBus系统
                var eventSystemManager = EventSystemManager.Instance;
                tests["EventSystemManager实例"] = eventSystemManager != null;
                tests["EventBus实例"] = eventSystemManager?.EventBus != null;
                
                // 检查适配器
                tests["UIEventSystemAdapter存在"] = FindObjectOfType<BlastingDesign.Events.Compatibility.UIEventSystemAdapter>() != null;
            }
            catch (Exception ex)
            {
                Logging.LogError("MigrationTestReport", $"功能测试异常: {ex.Message}");
                tests["功能测试异常"] = false;
            }
            
            return tests;
        }

        /// <summary>
        /// 获取性能测试数据
        /// </summary>
        private static Dictionary<string, string> GetPerformanceTestData()
        {
            var data = new Dictionary<string, string>();
            
            try
            {
                // 内存使用情况
                long memoryUsage = GC.GetTotalMemory(false);
                data["当前内存使用"] = $"{memoryUsage / 1024 / 1024:F2} MB";
                
                // 事件系统性能
                var eventSystemManager = EventSystemManager.Instance;
                if (eventSystemManager?.EventBus != null)
                {
                    data["EventBus状态"] = "正常运行";
                }
                else
                {
                    data["EventBus状态"] = "未初始化";
                }
                
                // 帧率信息
                data["当前帧率"] = $"{1f / Time.unscaledDeltaTime:F1} FPS";
                data["时间缩放"] = Time.timeScale.ToString("F2");
            }
            catch (Exception ex)
            {
                data["性能测试异常"] = ex.Message;
            }
            
            return data;
        }

        /// <summary>
        /// 获取兼容性测试结果
        /// </summary>
        private static Dictionary<string, bool> GetCompatibilityTests()
        {
            var tests = new Dictionary<string, bool>();
            
            try
            {
                // 旧事件系统兼容性
                var uiEventSystem = BlastingDesign.UI.Core.UIEventSystem.Instance;
                tests["UIEventSystem实例"] = uiEventSystem != null;
                
                // 新旧系统共存
                var eventSystemManager = EventSystemManager.Instance;
                tests["新旧系统共存"] = uiEventSystem != null && eventSystemManager != null;
                
                // 适配器功能
                var adapter = FindObjectOfType<BlastingDesign.Events.Compatibility.UIEventSystemAdapter>();
                tests["适配器功能"] = adapter != null;
                
                // 兼容性API
                tests["兼容性API可用"] = typeof(BlastingDesign.Events.Compatibility.UIEventSystemCompat) != null;
            }
            catch (Exception ex)
            {
                Logging.LogError("MigrationTestReport", $"兼容性测试异常: {ex.Message}");
                tests["兼容性测试异常"] = false;
            }
            
            return tests;
        }

        /// <summary>
        /// 查找对象的辅助方法
        /// </summary>
        private static T FindObjectOfType<T>() where T : UnityEngine.Object
        {
            return UnityEngine.Object.FindObjectOfType<T>();
        }

        /// <summary>
        /// 保存报告到文件
        /// </summary>
        /// <param name="report">报告内容</param>
        /// <param name="filename">文件名</param>
        public static void SaveReportToFile(string report, string filename = null)
        {
            if (string.IsNullOrEmpty(filename))
            {
                filename = $"ToolbarMigrationReport_{DateTime.Now:yyyyMMdd_HHmmss}.md";
            }
            
            try
            {
                string filePath = System.IO.Path.Combine(Application.dataPath, "..", filename);
                System.IO.File.WriteAllText(filePath, report);
                Logging.LogInfo("MigrationTestReport", $"报告已保存到: {filePath}");
            }
            catch (Exception ex)
            {
                Logging.LogError("MigrationTestReport", $"保存报告失败: {ex.Message}");
            }
        }
    }
}
