using System;
using UnityEngine;
using BlastingDesign.Events.Core;
using BlastingDesign.UI.Core;
using BlastingDesign.Utils;

namespace BlastingDesign.Events.Compatibility
{
    /// <summary>
    /// UIEventSystem兼容性支持类
    /// 提供简化的API来使用新事件系统，同时保持与旧系统的兼容性
    /// </summary>
    public static class UIEventSystemCompat
    {
        private static UIEventSystemAdapter _adapter;
        private static bool _isInitialized = false;

        /// <summary>
        /// 初始化兼容性支持
        /// </summary>
        /// <param name="adapter">事件系统适配器</param>
        public static void Initialize(UIEventSystemAdapter adapter)
        {
            _adapter = adapter;
            _isInitialized = true;

            Logging.LogInfo("UIEventSystemCompat", "兼容性支持已初始化");
        }

        /// <summary>
        /// 检查是否已初始化
        /// </summary>
        public static bool IsInitialized => _isInitialized && _adapter != null;

        #region 工具栏事件兼容方法

        /// <summary>
        /// 触发菜单项点击事件（兼容方法）
        /// </summary>
        /// <param name="menuItem">菜单项名称</param>
        public static void TriggerMenuItemClicked(string menuItem)
        {
            if (!IsInitialized)
            {
                Logging.LogWarning("UIEventSystemCompat", "兼容性支持未初始化，回退到旧系统");
                UIEventSystem.TriggerMenuItemClicked(menuItem);
                return;
            }

            try
            {
                _adapter.Publish(new MenuItemClickedEvent(menuItem));
                Logging.LogInfo("UIEventSystemCompat", $"触发菜单项点击事件: {menuItem}");
            }
            catch (Exception ex)
            {
                Logging.LogError("UIEventSystemCompat", $"触发菜单项点击事件失败: {ex.Message}");
                // 回退到旧系统
                UIEventSystem.TriggerMenuItemClicked(menuItem);
            }
        }

        /// <summary>
        /// 触发工具选择事件（兼容方法）
        /// </summary>
        /// <param name="toolName">工具名称</param>
        public static void TriggerToolSelected(string toolName)
        {
            if (!IsInitialized)
            {
                Logging.LogWarning("UIEventSystemCompat", "兼容性支持未初始化，回退到旧系统");
                UIEventSystem.TriggerToolSelected(toolName);
                return;
            }

            try
            {
                _adapter.Publish(new ToolSelectedEvent(toolName));
                Logging.LogInfo("UIEventSystemCompat", $"触发工具选择事件: {toolName}");
            }
            catch (Exception ex)
            {
                Logging.LogError("UIEventSystemCompat", $"触发工具选择事件失败: {ex.Message}");
                // 回退到旧系统
                UIEventSystem.TriggerToolSelected(toolName);
            }
        }

        /// <summary>
        /// 触发工具栏可见性变更事件
        /// </summary>
        /// <param name="isVisible">是否可见</param>
        public static void TriggerToolbarVisibilityChanged(bool isVisible)
        {
            if (!IsInitialized)
            {
                Logging.LogWarning("UIEventSystemCompat", "兼容性支持未初始化");
                return;
            }

            try
            {
                _adapter.Publish(new ToolbarVisibilityChangedEvent(isVisible));
                Logging.LogInfo("UIEventSystemCompat", $"触发工具栏可见性变更事件: {isVisible}");
            }
            catch (Exception ex)
            {
                Logging.LogError("UIEventSystemCompat", $"触发工具栏可见性变更事件失败: {ex.Message}");
            }
        }

        #endregion

        #region 选择事件兼容方法

        /// <summary>
        /// 触发对象选择事件（兼容方法）
        /// </summary>
        /// <param name="selectedObject">选中的对象</param>
        public static void TriggerObjectSelected(object selectedObject)
        {
            if (!IsInitialized)
            {
                Logging.LogWarning("UIEventSystemCompat", "兼容性支持未初始化，回退到旧系统");
                UIEventSystem.TriggerObjectSelected(selectedObject);
                return;
            }

            try
            {
                _adapter.Publish(new ObjectSelectedEvent(selectedObject));
                Logging.LogInfo("UIEventSystemCompat", $"触发对象选择事件: {selectedObject}");
            }
            catch (Exception ex)
            {
                Logging.LogError("UIEventSystemCompat", $"触发对象选择事件失败: {ex.Message}");
                // 回退到旧系统
                UIEventSystem.TriggerObjectSelected(selectedObject);
            }
        }

        /// <summary>
        /// 触发多对象选择事件
        /// </summary>
        /// <param name="selectedObjects">选中的对象数组</param>
        public static void TriggerMultipleObjectsSelected(object[] selectedObjects)
        {
            if (!IsInitialized)
            {
                Logging.LogWarning("UIEventSystemCompat", "兼容性支持未初始化");
                return;
            }

            try
            {
                _adapter.Publish(new MultipleObjectsSelectedEvent(selectedObjects));
                Logging.LogInfo("UIEventSystemCompat", $"触发多对象选择事件: {selectedObjects.Length} 个对象");
            }
            catch (Exception ex)
            {
                Logging.LogError("UIEventSystemCompat", $"触发多对象选择事件失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 触发选择清除事件
        /// </summary>
        public static void TriggerSelectionCleared()
        {
            if (!IsInitialized)
            {
                Logging.LogWarning("UIEventSystemCompat", "兼容性支持未初始化");
                return;
            }

            try
            {
                _adapter.Publish(new SelectionClearedEvent());
                Logging.LogInfo("UIEventSystemCompat", "触发选择清除事件");
            }
            catch (Exception ex)
            {
                Logging.LogError("UIEventSystemCompat", $"触发选择清除事件失败: {ex.Message}");
            }
        }

        #endregion

        #region 状态栏事件兼容方法

        /// <summary>
        /// 触发状态消息事件（兼容方法）
        /// </summary>
        /// <param name="message">状态消息</param>
        public static void TriggerStatusMessage(string message)
        {
            if (!IsInitialized)
            {
                Logging.LogWarning("UIEventSystemCompat", "兼容性支持未初始化，回退到旧系统");
                UIEventSystem.TriggerStatusMessage(message);
                return;
            }

            try
            {
                _adapter.Publish(new StatusMessageEvent(message));
                Logging.LogInfo("UIEventSystemCompat", $"触发状态消息事件: {message}");
            }
            catch (Exception ex)
            {
                Logging.LogError("UIEventSystemCompat", $"触发状态消息事件失败: {ex.Message}");
                // 回退到旧系统
                UIEventSystem.TriggerStatusMessage(message);
            }
        }

        /// <summary>
        /// 触发鼠标世界坐标变更事件
        /// </summary>
        /// <param name="worldPosition">世界坐标</param>
        public static void TriggerMouseWorldPositionChanged(Vector3 worldPosition)
        {
            if (!IsInitialized) return;

            try
            {
                _adapter.Publish(new MouseWorldPositionChangedEvent(worldPosition));
            }
            catch (Exception ex)
            {
                Logging.LogError("UIEventSystemCompat", $"触发鼠标世界坐标变更事件失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 触发鼠标地理坐标变更事件
        /// </summary>
        /// <param name="geoPosition">地理坐标</param>
        public static void TriggerMouseGeoPositionChanged(Vector2 geoPosition)
        {
            if (!IsInitialized) return;

            try
            {
                _adapter.Publish(new MouseGeoPositionChangedEvent(geoPosition));
            }
            catch (Exception ex)
            {
                Logging.LogError("UIEventSystemCompat", $"触发鼠标地理坐标变更事件失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 触发工具提示变更事件
        /// </summary>
        /// <param name="toolTip">工具提示</param>
        public static void TriggerToolTipChanged(string toolTip)
        {
            if (!IsInitialized) return;

            try
            {
                _adapter.Publish(new ToolTipChangedEvent(toolTip));
            }
            catch (Exception ex)
            {
                Logging.LogError("UIEventSystemCompat", $"触发工具提示变更事件失败: {ex.Message}");
            }
        }

        #endregion

        #region 面板事件兼容方法

        /// <summary>
        /// 触发面板折叠事件
        /// </summary>
        /// <param name="panelName">面板名称</param>
        /// <param name="isCollapsed">是否折叠</param>
        public static void TriggerPanelCollapsed(string panelName, bool isCollapsed)
        {
            if (!IsInitialized) return;

            try
            {
                _adapter.Publish(new PanelCollapsedEvent(panelName, isCollapsed));
                Logging.LogInfo("UIEventSystemCompat", $"触发面板折叠事件: {panelName} - {isCollapsed}");
            }
            catch (Exception ex)
            {
                Logging.LogError("UIEventSystemCompat", $"触发面板折叠事件失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 触发面板调整大小事件
        /// </summary>
        /// <param name="panelName">面板名称</param>
        /// <param name="newSize">新大小</param>
        public static void TriggerPanelResized(string panelName, float newSize)
        {
            if (!IsInitialized) return;

            try
            {
                _adapter.Publish(new PanelResizedEvent(panelName, newSize));
                Logging.LogInfo("UIEventSystemCompat", $"触发面板调整大小事件: {panelName} - {newSize}");
            }
            catch (Exception ex)
            {
                Logging.LogError("UIEventSystemCompat", $"触发面板调整大小事件失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 触发面板可见性变更事件
        /// </summary>
        /// <param name="panelName">面板名称</param>
        /// <param name="isVisible">是否可见</param>
        public static void TriggerPanelVisibilityChanged(string panelName, bool isVisible)
        {
            if (!IsInitialized) return;

            try
            {
                _adapter.Publish(new PanelVisibilityChangedEvent(panelName, isVisible));
                Logging.LogInfo("UIEventSystemCompat", $"触发面板可见性变更事件: {panelName} - {isVisible}");
            }
            catch (Exception ex)
            {
                Logging.LogError("UIEventSystemCompat", $"触发面板可见性变更事件失败: {ex.Message}");
            }
        }

        #endregion

        #region 自定义事件兼容方法

        /// <summary>
        /// 触发自定义事件（兼容方法）
        /// </summary>
        /// <param name="eventName">事件名称</param>
        /// <param name="data">事件数据</param>
        public static void TriggerCustomEvent(string eventName, object data = null)
        {
            if (!IsInitialized)
            {
                Logging.LogWarning("UIEventSystemCompat", "兼容性支持未初始化，回退到旧系统");
                UIEventSystem.TriggerCustomEvent(eventName, data);
                return;
            }

            try
            {
                _adapter.Publish(new CustomEvent(eventName, data));
                Logging.LogInfo("UIEventSystemCompat", $"触发自定义事件: {eventName}");
            }
            catch (Exception ex)
            {
                Logging.LogError("UIEventSystemCompat", $"触发自定义事件失败: {ex.Message}");
                // 回退到旧系统
                UIEventSystem.TriggerCustomEvent(eventName, data);
            }
        }

        #endregion

        #region 订阅方法

        /// <summary>
        /// 订阅事件（简化API）
        /// </summary>
        /// <typeparam name="T">事件类型</typeparam>
        /// <param name="handler">事件处理器</param>
        /// <returns>订阅句柄</returns>
        public static IEventSubscription Subscribe<T>(Action<T> handler) where T : IEvent
        {
            if (!IsInitialized)
            {
                Logging.LogError("UIEventSystemCompat", "兼容性支持未初始化，无法订阅事件");
                return null;
            }

            try
            {
                return _adapter.Subscribe(handler);
            }
            catch (Exception ex)
            {
                Logging.LogError("UIEventSystemCompat", $"订阅事件失败: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 取消订阅事件
        /// </summary>
        /// <param name="subscription">订阅句柄</param>
        public static void Unsubscribe(IEventSubscription subscription)
        {
            if (!IsInitialized || subscription == null) return;

            try
            {
                _adapter.Unsubscribe(subscription);
            }
            catch (Exception ex)
            {
                Logging.LogError("UIEventSystemCompat", $"取消订阅事件失败: {ex.Message}");
            }
        }

        #endregion

        #region 便捷订阅方法

        /// <summary>
        /// 订阅菜单项点击事件
        /// </summary>
        /// <param name="handler">事件处理器</param>
        /// <returns>订阅句柄</returns>
        public static IEventSubscription SubscribeMenuItemClicked(Action<MenuItemClickedEvent> handler)
        {
            return Subscribe(handler);
        }

        /// <summary>
        /// 订阅工具选择事件
        /// </summary>
        /// <param name="handler">事件处理器</param>
        /// <returns>订阅句柄</returns>
        public static IEventSubscription SubscribeToolSelected(Action<ToolSelectedEvent> handler)
        {
            return Subscribe(handler);
        }

        /// <summary>
        /// 订阅对象选择事件
        /// </summary>
        /// <param name="handler">事件处理器</param>
        /// <returns>订阅句柄</returns>
        public static IEventSubscription SubscribeObjectSelected(Action<ObjectSelectedEvent> handler)
        {
            return Subscribe(handler);
        }

        /// <summary>
        /// 订阅状态消息事件
        /// </summary>
        /// <param name="handler">事件处理器</param>
        /// <returns>订阅句柄</returns>
        public static IEventSubscription SubscribeStatusMessage(Action<StatusMessageEvent> handler)
        {
            return Subscribe(handler);
        }

        /// <summary>
        /// 订阅面板折叠事件
        /// </summary>
        /// <param name="handler">事件处理器</param>
        /// <returns>订阅句柄</returns>
        public static IEventSubscription SubscribePanelCollapsed(Action<PanelCollapsedEvent> handler)
        {
            return Subscribe(handler);
        }

        /// <summary>
        /// 订阅面板调整大小事件
        /// </summary>
        /// <param name="handler">事件处理器</param>
        /// <returns>订阅句柄</returns>
        public static IEventSubscription SubscribePanelResized(Action<PanelResizedEvent> handler)
        {
            return Subscribe(handler);
        }

        /// <summary>
        /// 订阅面板可见性变更事件
        /// </summary>
        /// <param name="handler">事件处理器</param>
        /// <returns>订阅句柄</returns>
        public static IEventSubscription SubscribePanelVisibilityChanged(Action<PanelVisibilityChangedEvent> handler)
        {
            return Subscribe(handler);
        }

        /// <summary>
        /// 订阅按键按下事件
        /// </summary>
        /// <param name="handler">事件处理器</param>
        /// <returns>订阅句柄</returns>
        public static IEventSubscription SubscribeKeyPress(Action<KeyPressEvent> handler)
        {
            return Subscribe(handler);
        }

        /// <summary>
        /// 订阅鼠标点击事件
        /// </summary>
        /// <param name="handler">事件处理器</param>
        /// <returns>订阅句柄</returns>
        public static IEventSubscription SubscribeMouseClick(Action<MouseClickEvent> handler)
        {
            return Subscribe(handler);
        }

        /// <summary>
        /// 订阅自定义事件
        /// </summary>
        /// <param name="handler">事件处理器</param>
        /// <returns>订阅句柄</returns>
        public static IEventSubscription SubscribeCustomEvent(Action<CustomEvent> handler)
        {
            return Subscribe(handler);
        }

        /// <summary>
        /// 订阅多对象选择事件
        /// </summary>
        /// <param name="handler">事件处理器</param>
        /// <returns>订阅句柄</returns>
        public static IEventSubscription SubscribeMultipleObjectsSelected(Action<MultipleObjectsSelectedEvent> handler)
        {
            return Subscribe(handler);
        }

        /// <summary>
        /// 订阅选择清除事件
        /// </summary>
        /// <param name="handler">事件处理器</param>
        /// <returns>订阅句柄</returns>
        public static IEventSubscription SubscribeSelectionCleared(Action<SelectionClearedEvent> handler)
        {
            return Subscribe(handler);
        }

        /// <summary>
        /// 订阅工具栏可见性变更事件
        /// </summary>
        /// <param name="handler">事件处理器</param>
        /// <returns>订阅句柄</returns>
        public static IEventSubscription SubscribeToolbarVisibilityChanged(Action<ToolbarVisibilityChangedEvent> handler)
        {
            return Subscribe(handler);
        }

        /// <summary>
        /// 订阅鼠标世界坐标变更事件
        /// </summary>
        /// <param name="handler">事件处理器</param>
        /// <returns>订阅句柄</returns>
        public static IEventSubscription SubscribeMouseWorldPositionChanged(Action<MouseWorldPositionChangedEvent> handler)
        {
            return Subscribe(handler);
        }

        /// <summary>
        /// 订阅鼠标地理坐标变更事件
        /// </summary>
        /// <param name="handler">事件处理器</param>
        /// <returns>订阅句柄</returns>
        public static IEventSubscription SubscribeMouseGeoPositionChanged(Action<MouseGeoPositionChangedEvent> handler)
        {
            return Subscribe(handler);
        }

        /// <summary>
        /// 订阅工具提示变更事件
        /// </summary>
        /// <param name="handler">事件处理器</param>
        /// <returns>订阅句柄</returns>
        public static IEventSubscription SubscribeToolTipChanged(Action<ToolTipChangedEvent> handler)
        {
            return Subscribe(handler);
        }

        /// <summary>
        /// 订阅属性变更事件
        /// </summary>
        /// <param name="handler">事件处理器</param>
        /// <returns>订阅句柄</returns>
        public static IEventSubscription SubscribePropertyChanged(Action<PropertyChangedEvent> handler)
        {
            return Subscribe(handler);
        }

        /// <summary>
        /// 订阅按钮点击事件
        /// </summary>
        /// <param name="handler">事件处理器</param>
        /// <returns>订阅句柄</returns>
        public static IEventSubscription SubscribeButtonClick(Action<ButtonClickEvent> handler)
        {
            return Subscribe(handler);
        }

        /// <summary>
        /// 订阅菜单选择事件
        /// </summary>
        /// <param name="handler">事件处理器</param>
        /// <returns>订阅句柄</returns>
        public static IEventSubscription SubscribeMenuSelect(Action<MenuSelectEvent> handler)
        {
            return Subscribe(handler);
        }

        #endregion
    }
}
