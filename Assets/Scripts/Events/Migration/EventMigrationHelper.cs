using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using UnityEngine;
using UnityEngine.Events;
using BlastingDesign.Events.Core;
using BlastingDesign.UI.Core;
using BlastingDesign.Utils;

namespace BlastingDesign.Events.Migration
{
    /// <summary>
    /// 事件迁移助手
    /// 帮助新旧事件系统之间的桥接和转换
    /// </summary>
    public class EventMigrationHelper : MonoBehaviour
    {
        private static EventMigrationHelper _instance;
        public static EventMigrationHelper Instance
        {
            get
            {
                if (_instance == null)
                {
                    _instance = FindFirstObjectByType<EventMigrationHelper>();
                    if (_instance == null)
                    {
                        GameObject go = new("EventMigrationHelper");
                        _instance = go.AddComponent<EventMigrationHelper>();
                        DontDestroyOnLoad(go);
                    }
                }
                return _instance;
            }
        }

        private IEventBus _eventBus;
        private Dictionary<string, object> _legacyEvents; // 改为object类型以支持不同类型的UnityEvent
        private Dictionary<string, List<IEventSubscription>> _bridgeSubscriptions;

        private void Awake()
        {
            if (_instance != null && _instance != this)
            {
                Destroy(gameObject);
                return;
            }

            _instance = this;
            DontDestroyOnLoad(gameObject);

            Initialize();
        }

        private void Initialize()
        {
            _eventBus = new EventBus(debugMode: true); // 临时创建，实际使用时需要从单例获取
            _legacyEvents = new Dictionary<string, object>();
            _bridgeSubscriptions = new Dictionary<string, List<IEventSubscription>>();
        }

        #region 新事件系统到旧事件系统的桥接

        /// <summary>
        /// 注册新事件到旧UnityEvent的桥接
        /// </summary>
        /// <typeparam name="T">新事件类型</typeparam>
        /// <param name="legacyEventName">旧事件名称</param>
        /// <param name="legacyEvent">旧UnityEvent</param>
        /// <param name="converter">事件转换器</param>
        public void BridgeNewToLegacy<T>(string legacyEventName, UnityEvent legacyEvent, Action<T> converter = null) where T : IEvent
        {
            if (!_legacyEvents.ContainsKey(legacyEventName))
            {
                _legacyEvents[legacyEventName] = legacyEvent;
            }

            var subscription = _eventBus.Subscribe<T>(eventData =>
            {
                try
                {
                    converter?.Invoke(eventData);
                    legacyEvent?.Invoke();
                }
                catch (Exception ex)
                {
                    Debug.LogError($"事件桥接错误 (新->旧): {ex.Message}");
                }
            });

            AddBridgeSubscription(legacyEventName, subscription);
        }

        /// <summary>
        /// 注册新事件到旧UnityEvent<T>的桥接
        /// </summary>
        /// <typeparam name="TEvent">新事件类型</typeparam>
        /// <typeparam name="TLegacy">旧事件参数类型</typeparam>
        /// <param name="legacyEventName">旧事件名称</param>
        /// <param name="legacyEvent">旧UnityEvent<T></param>
        /// <param name="converter">事件转换器</param>
        public void BridgeNewToLegacy<TEvent, TLegacy>(string legacyEventName, UnityEvent<TLegacy> legacyEvent, Func<TEvent, TLegacy> converter) where TEvent : IEvent
        {
            var subscription = _eventBus.Subscribe<TEvent>(eventData =>
            {
                try
                {
                    var legacyData = converter(eventData);
                    legacyEvent?.Invoke(legacyData);
                }
                catch (Exception ex)
                {
                    Debug.LogError($"事件桥接错误 (新->旧): {ex.Message}");
                }
            });

            AddBridgeSubscription(legacyEventName, subscription);
        }

        #endregion

        #region 旧事件系统到新事件系统的桥接

        /// <summary>
        /// 注册旧UnityEvent到新事件系统的桥接
        /// </summary>
        /// <typeparam name="T">新事件类型</typeparam>
        /// <param name="legacyEventName">旧事件名称</param>
        /// <param name="legacyEvent">旧UnityEvent</param>
        /// <param name="eventFactory">事件工厂方法</param>
        public void BridgeLegacyToNew<T>(string legacyEventName, UnityEvent legacyEvent, Func<T> eventFactory) where T : IEvent
        {
            legacyEvent.AddListener(() =>
            {
                try
                {
                    var newEvent = eventFactory();
                    _eventBus.Publish(newEvent);
                }
                catch (Exception ex)
                {
                    Debug.LogError($"事件桥接错误 (旧->新): {ex.Message}");
                }
            });

            _legacyEvents[legacyEventName] = legacyEvent;
        }

        /// <summary>
        /// 注册旧UnityEvent<T>到新事件系统的桥接
        /// </summary>
        /// <typeparam name="TLegacy">旧事件参数类型</typeparam>
        /// <typeparam name="TEvent">新事件类型</typeparam>
        /// <param name="legacyEventName">旧事件名称</param>
        /// <param name="legacyEvent">旧UnityEvent<T></param>
        /// <param name="eventFactory">事件工厂方法</param>
        public void BridgeLegacyToNew<TLegacy, TEvent>(string legacyEventName, UnityEvent<TLegacy> legacyEvent, Func<TLegacy, TEvent> eventFactory) where TEvent : IEvent
        {
            legacyEvent.AddListener(legacyData =>
            {
                try
                {
                    var newEvent = eventFactory(legacyData);
                    _eventBus.Publish(newEvent);
                }
                catch (Exception ex)
                {
                    Debug.LogError($"事件桥接错误 (旧->新): {ex.Message}");
                }
            });

            // 记录桥接信息
            _legacyEvents[legacyEventName] = legacyEvent;
        }

        #endregion

        #region 双向桥接

        /// <summary>
        /// 建立双向桥接，新旧事件系统可以相互触发
        /// </summary>
        /// <typeparam name="T">新事件类型</typeparam>
        /// <param name="legacyEventName">旧事件名称</param>
        /// <param name="legacyEvent">旧UnityEvent</param>
        /// <param name="newToLegacyConverter">新->旧转换器</param>
        /// <param name="legacyToNewFactory">旧->新工厂方法</param>
        public void BridgeBidirectional<T>(string legacyEventName, UnityEvent legacyEvent, Action<T> newToLegacyConverter, Func<T> legacyToNewFactory) where T : IEvent
        {
            BridgeNewToLegacy(legacyEventName, legacyEvent, newToLegacyConverter);
            BridgeLegacyToNew(legacyEventName, legacyEvent, legacyToNewFactory);
        }

        #endregion

        #region 迁移工具方法

        /// <summary>
        /// 检查组件是否已经迁移到新事件系统
        /// </summary>
        /// <param name="componentName">组件名称</param>
        /// <returns>是否已迁移</returns>
        public bool IsComponentMigrated(string componentName)
        {
            // 检查是否还有该组件的桥接存在
            return !_bridgeSubscriptions.ContainsKey(componentName) || _bridgeSubscriptions[componentName].Count == 0;
        }

        /// <summary>
        /// 标记组件为已迁移
        /// </summary>
        /// <param name="componentName">组件名称</param>
        public void MarkComponentMigrated(string componentName)
        {
            // 这里可以添加迁移状态记录逻辑
            Debug.Log($"组件 {componentName} 已迁移到新事件系统");
        }

        /// <summary>
        /// 移除指定组件的桥接
        /// </summary>
        /// <param name="componentName">组件名称</param>
        public void RemoveBridge(string componentName)
        {
            if (_bridgeSubscriptions.ContainsKey(componentName))
            {
                var subscriptions = _bridgeSubscriptions[componentName];
                foreach (var subscription in subscriptions)
                {
                    subscription?.Dispose();
                }
                _bridgeSubscriptions.Remove(componentName);
            }

            if (_legacyEvents.ContainsKey(componentName))
            {
                _legacyEvents.Remove(componentName);
            }
        }

        /// <summary>
        /// 获取迁移进度报告
        /// </summary>
        /// <returns>迁移进度信息</returns>
        public string GetMigrationReport()
        {
            int totalBridges = _bridgeSubscriptions.Count;
            int activeBridges = 0;

            foreach (var kvp in _bridgeSubscriptions)
            {
                if (kvp.Value.Count > 0)
                {
                    activeBridges++;
                }
            }

            return $"迁移进度: {activeBridges}/{totalBridges} 个桥接仍在使用";
        }

        #endregion

        #region 私有方法

        private void AddBridgeSubscription(string bridgeName, IEventSubscription subscription)
        {
            if (!_bridgeSubscriptions.ContainsKey(bridgeName))
            {
                _bridgeSubscriptions[bridgeName] = new List<IEventSubscription>();
            }
            _bridgeSubscriptions[bridgeName].Add(subscription);
        }

        #endregion

        #region 迁移验证功能

        /// <summary>
        /// 验证事件系统迁移状态
        /// </summary>
        /// <returns>迁移验证结果</returns>
        public MigrationValidationResult ValidateMigration()
        {
            var result = new MigrationValidationResult();

            try
            {
                // 验证事件类型映射
                ValidateEventTypeMappings(result);

                // 验证适配器功能
                ValidateAdapterFunctionality(result);

                // 验证组件兼容性
                ValidateComponentCompatibility(result);

                result.IsValid = result.Errors.Count == 0;
                result.ValidationTime = DateTime.UtcNow;

                Logging.LogInfo("EventMigrationHelper", $"迁移验证完成: {(result.IsValid ? "成功" : "失败")}");

                if (!result.IsValid)
                {
                    foreach (var error in result.Errors)
                    {
                        Logging.LogError("EventMigrationHelper", $"验证错误: {error}");
                    }
                }
            }
            catch (Exception ex)
            {
                result.Errors.Add($"验证过程中发生异常: {ex.Message}");
                result.IsValid = false;
                Logging.LogError("EventMigrationHelper", $"迁移验证异常: {ex.Message}");
            }

            return result;
        }

        /// <summary>
        /// 验证事件类型映射
        /// </summary>
        private void ValidateEventTypeMappings(MigrationValidationResult result)
        {
            var requiredEventTypes = GetRequiredEventTypes();
            var existingEventTypes = GetExistingEventTypes();

            foreach (var requiredType in requiredEventTypes)
            {
                if (!existingEventTypes.Contains(requiredType))
                {
                    result.Errors.Add($"缺失事件类型: {requiredType}");
                }
                else
                {
                    result.ValidatedEventTypes.Add(requiredType);
                }
            }

            Logging.LogInfo("EventMigrationHelper", $"验证了 {result.ValidatedEventTypes.Count} 个事件类型");
        }

        /// <summary>
        /// 验证适配器功能
        /// </summary>
        private void ValidateAdapterFunctionality(MigrationValidationResult result)
        {
            try
            {
                var legacySystem = UIEventSystem.Instance;
                if (legacySystem == null)
                {
                    result.Errors.Add("UIEventSystem实例未找到");
                    return;
                }

                // 检查适配器是否正确初始化
                var eventSystemManager = EventSystemManager.Instance;
                if (eventSystemManager == null)
                {
                    result.Errors.Add("EventSystemManager实例未找到");
                    return;
                }

                var eventBus = eventSystemManager.EventBus;
                if (eventBus == null)
                {
                    result.Errors.Add("EventBus实例未找到");
                    return;
                }

                result.AdapterValidated = true;
                Logging.LogInfo("EventMigrationHelper", "适配器功能验证通过");
            }
            catch (Exception ex)
            {
                result.Errors.Add($"适配器验证失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 验证组件兼容性
        /// </summary>
        private void ValidateComponentCompatibility(MigrationValidationResult result)
        {
            var components = GetUIComponents();

            foreach (var component in components)
            {
                try
                {
                    ValidateComponentEventUsage(component, result);
                }
                catch (Exception ex)
                {
                    result.Errors.Add($"组件 {component.GetType().Name} 验证失败: {ex.Message}");
                }
            }

            Logging.LogInfo("EventMigrationHelper", $"验证了 {components.Count} 个UI组件");
        }

        #endregion

        #region 自动化迁移功能

        /// <summary>
        /// 自动迁移组件事件处理
        /// </summary>
        /// <param name="component">要迁移的组件</param>
        /// <returns>迁移结果</returns>
        public ComponentMigrationResult MigrateComponent(MonoBehaviour component)
        {
            var result = new ComponentMigrationResult
            {
                ComponentName = component.GetType().Name,
                ComponentType = component.GetType()
            };

            try
            {
                // 分析组件中的UnityEvent使用
                AnalyzeUnityEventUsage(component, result);

                // 生成迁移建议
                GenerateMigrationSuggestions(result);

                result.IsSuccessful = result.Errors.Count == 0;
                result.MigrationTime = DateTime.UtcNow;

                Logging.LogInfo("EventMigrationHelper", $"组件 {result.ComponentName} 迁移分析完成");
            }
            catch (Exception ex)
            {
                result.Errors.Add($"迁移分析失败: {ex.Message}");
                result.IsSuccessful = false;
                Logging.LogError("EventMigrationHelper", $"组件迁移异常: {ex.Message}");
            }

            return result;
        }

        /// <summary>
        /// 分析UnityEvent使用情况
        /// </summary>
        private void AnalyzeUnityEventUsage(MonoBehaviour component, ComponentMigrationResult result)
        {
            var fields = component.GetType().GetFields(BindingFlags.Public | BindingFlags.NonPublic | BindingFlags.Instance);

            foreach (var field in fields)
            {
                if (IsUnityEventField(field))
                {
                    var eventInfo = new UnityEventInfo
                    {
                        FieldName = field.Name,
                        FieldType = field.FieldType,
                        EventValue = field.GetValue(component)
                    };

                    result.UnityEvents.Add(eventInfo);
                }
            }
        }

        /// <summary>
        /// 生成迁移建议
        /// </summary>
        private void GenerateMigrationSuggestions(ComponentMigrationResult result)
        {
            foreach (var unityEvent in result.UnityEvents)
            {
                var suggestion = CreateMigrationSuggestion(unityEvent);
                if (suggestion != null)
                {
                    result.MigrationSuggestions.Add(suggestion);
                }
            }
        }

        #endregion

        #region 辅助方法

        /// <summary>
        /// 获取必需的事件类型列表
        /// </summary>
        private List<string> GetRequiredEventTypes()
        {
            return new List<string>
            {
                "MenuItemClickedEvent",
                "ToolSelectedEvent",
                "ToolbarVisibilityChangedEvent",
                "ObjectSelectedEvent",
                "MultipleObjectsSelectedEvent",
                "SelectionClearedEvent",
                "StatusMessageEvent",
                "MouseWorldPositionChangedEvent",
                "MouseGeoPositionChangedEvent",
                "ToolTipChangedEvent",
                "PanelEvent",
                "PanelCollapsedEvent",
                "PanelResizedEvent",
                "PanelVisibilityChangedEvent",
                "PropertyChangedEvent",
                "PropertyEditorRequestedEvent",
                "PropertiesRefreshRequestedEvent",
                "CustomEvent"
            };
        }

        /// <summary>
        /// 获取现有的事件类型列表
        /// </summary>
        private List<string> GetExistingEventTypes()
        {
            var eventTypes = new List<string>();

            try
            {
                var assemblies = AppDomain.CurrentDomain.GetAssemblies();
                foreach (var assembly in assemblies)
                {
                    try
                    {
                        var types = assembly.GetTypes()
                            .Where(t => typeof(IEvent).IsAssignableFrom(t) && !t.IsInterface && !t.IsAbstract)
                            .Select(t => t.Name)
                            .ToList();

                        eventTypes.AddRange(types);
                    }
                    catch (ReflectionTypeLoadException)
                    {
                        // 忽略无法加载的程序集
                    }
                }
            }
            catch (Exception ex)
            {
                Logging.LogError("EventMigrationHelper", $"获取事件类型时出错: {ex.Message}");
            }

            return eventTypes.Distinct().ToList();
        }

        /// <summary>
        /// 获取UI组件列表
        /// </summary>
        private List<MonoBehaviour> GetUIComponents()
        {
            var components = new List<MonoBehaviour>();

            try
            {
                // 查找所有UI相关的MonoBehaviour组件
                var allComponents = UnityEngine.Object.FindObjectsOfType<MonoBehaviour>();

                foreach (var component in allComponents)
                {
                    if (IsUIComponent(component))
                    {
                        components.Add(component);
                    }
                }
            }
            catch (Exception ex)
            {
                Logging.LogError("EventMigrationHelper", $"获取UI组件时出错: {ex.Message}");
            }

            return components;
        }

        /// <summary>
        /// 验证组件事件使用
        /// </summary>
        private void ValidateComponentEventUsage(MonoBehaviour component, MigrationValidationResult result)
        {
            var componentType = component.GetType();

            // 检查是否使用了UIEventSystem
            var fields = componentType.GetFields(BindingFlags.Public | BindingFlags.NonPublic | BindingFlags.Instance);
            bool usesUIEventSystem = fields.Any(f => f.FieldType == typeof(UIEventSystem));

            if (usesUIEventSystem)
            {
                result.ComponentsUsingLegacyEvents.Add(componentType.Name);
            }

            // 检查UnityEvent使用
            var unityEventFields = fields.Where(IsUnityEventField).ToList();
            if (unityEventFields.Any())
            {
                result.ComponentsWithUnityEvents.Add(componentType.Name);
            }
        }

        /// <summary>
        /// 判断是否为UI组件
        /// </summary>
        private bool IsUIComponent(MonoBehaviour component)
        {
            var componentType = component.GetType();
            var namespaceName = componentType.Namespace;

            return namespaceName != null &&
                   (namespaceName.Contains("UI") ||
                    namespaceName.Contains("BlastingDesign.UI") ||
                    componentType.Name.Contains("UI") ||
                    componentType.Name.Contains("Panel") ||
                    componentType.Name.Contains("Toolbar") ||
                    componentType.Name.Contains("StatusBar") ||
                    componentType.Name.Contains("MenuBar"));
        }

        /// <summary>
        /// 判断是否为UnityEvent字段
        /// </summary>
        private bool IsUnityEventField(FieldInfo field)
        {
            return typeof(UnityEventBase).IsAssignableFrom(field.FieldType);
        }

        /// <summary>
        /// 创建迁移建议
        /// </summary>
        private MigrationSuggestion CreateMigrationSuggestion(UnityEventInfo unityEvent)
        {
            // 根据字段名称和类型生成迁移建议
            var suggestion = new MigrationSuggestion
            {
                OriginalEventName = unityEvent.FieldName,
                OriginalEventType = unityEvent.FieldType.Name
            };

            // 基于命名约定推荐新的事件类型
            if (unityEvent.FieldName.Contains("MenuItemClicked"))
            {
                suggestion.RecommendedEventType = "MenuItemClickedEvent";
                suggestion.MigrationCode = GenerateMenuItemMigrationCode(unityEvent);
            }
            else if (unityEvent.FieldName.Contains("ToolSelected"))
            {
                suggestion.RecommendedEventType = "ToolSelectedEvent";
                suggestion.MigrationCode = GenerateToolSelectedMigrationCode(unityEvent);
            }
            // 可以添加更多的映射规则

            return suggestion;
        }

        /// <summary>
        /// 生成菜单项迁移代码
        /// </summary>
        private string GenerateMenuItemMigrationCode(UnityEventInfo unityEvent)
        {
            return $@"
// 替换 {unityEvent.FieldName}
// 旧代码: {unityEvent.FieldName}.Invoke(menuItem);
// 新代码:
_eventBus.Publish(new MenuItemClickedEvent(menuItem));
";
        }

        /// <summary>
        /// 生成工具选择迁移代码
        /// </summary>
        private string GenerateToolSelectedMigrationCode(UnityEventInfo unityEvent)
        {
            return $@"
// 替换 {unityEvent.FieldName}
// 旧代码: {unityEvent.FieldName}.Invoke(toolName);
// 新代码:
_eventBus.Publish(new ToolSelectedEvent(toolName));
";
        }

        #endregion

        #region 清理

        private void OnDestroy()
        {
            // 清理所有桥接订阅
            foreach (var subscriptions in _bridgeSubscriptions.Values)
            {
                foreach (var subscription in subscriptions)
                {
                    subscription?.Dispose();
                }
            }
            _bridgeSubscriptions.Clear();
            _legacyEvents.Clear();
        }

        #endregion
    }
}