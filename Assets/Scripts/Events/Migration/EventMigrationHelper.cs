using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Events;
using BlastingDesign.Events.Core;

namespace BlastingDesign.Events.Migration
{
    /// <summary>
    /// 事件迁移助手
    /// 帮助新旧事件系统之间的桥接和转换
    /// </summary>
    public class EventMigrationHelper : MonoBehaviour
    {
        private static EventMigrationHelper _instance;
        public static EventMigrationHelper Instance
        {
            get
            {
                if (_instance == null)
                {
                    _instance = FindFirstObjectByType<EventMigrationHelper>();
                    if (_instance == null)
                    {
                        GameObject go = new("EventMigrationHelper");
                        _instance = go.AddComponent<EventMigrationHelper>();
                        DontDestroyOnLoad(go);
                    }
                }
                return _instance;
            }
        }

        private IEventBus _eventBus;
        private Dictionary<string, object> _legacyEvents; // 改为object类型以支持不同类型的UnityEvent
        private Dictionary<string, List<IEventSubscription>> _bridgeSubscriptions;

        private void Awake()
        {
            if (_instance != null && _instance != this)
            {
                Destroy(gameObject);
                return;
            }
            
            _instance = this;
            DontDestroyOnLoad(gameObject);
            
            Initialize();
        }

        private void Initialize()
        {
            _eventBus = new EventBus(debugMode: true); // 临时创建，实际使用时需要从单例获取
            _legacyEvents = new Dictionary<string, object>();
            _bridgeSubscriptions = new Dictionary<string, List<IEventSubscription>>();
        }

        #region 新事件系统到旧事件系统的桥接

        /// <summary>
        /// 注册新事件到旧UnityEvent的桥接
        /// </summary>
        /// <typeparam name="T">新事件类型</typeparam>
        /// <param name="legacyEventName">旧事件名称</param>
        /// <param name="legacyEvent">旧UnityEvent</param>
        /// <param name="converter">事件转换器</param>
        public void BridgeNewToLegacy<T>(string legacyEventName, UnityEvent legacyEvent, Action<T> converter = null) where T : IEvent
        {
            if (!_legacyEvents.ContainsKey(legacyEventName))
            {
                _legacyEvents[legacyEventName] = legacyEvent;
            }

            var subscription = _eventBus.Subscribe<T>(eventData =>
            {
                try
                {
                    converter?.Invoke(eventData);
                    legacyEvent?.Invoke();
                }
                catch (Exception ex)
                {
                    Debug.LogError($"事件桥接错误 (新->旧): {ex.Message}");
                }
            });

            AddBridgeSubscription(legacyEventName, subscription);
        }

        /// <summary>
        /// 注册新事件到旧UnityEvent<T>的桥接
        /// </summary>
        /// <typeparam name="TEvent">新事件类型</typeparam>
        /// <typeparam name="TLegacy">旧事件参数类型</typeparam>
        /// <param name="legacyEventName">旧事件名称</param>
        /// <param name="legacyEvent">旧UnityEvent<T></param>
        /// <param name="converter">事件转换器</param>
        public void BridgeNewToLegacy<TEvent, TLegacy>(string legacyEventName, UnityEvent<TLegacy> legacyEvent, Func<TEvent, TLegacy> converter) where TEvent : IEvent
        {
            var subscription = _eventBus.Subscribe<TEvent>(eventData =>
            {
                try
                {
                    var legacyData = converter(eventData);
                    legacyEvent?.Invoke(legacyData);
                }
                catch (Exception ex)
                {
                    Debug.LogError($"事件桥接错误 (新->旧): {ex.Message}");
                }
            });

            AddBridgeSubscription(legacyEventName, subscription);
        }

        #endregion

        #region 旧事件系统到新事件系统的桥接

        /// <summary>
        /// 注册旧UnityEvent到新事件系统的桥接
        /// </summary>
        /// <typeparam name="T">新事件类型</typeparam>
        /// <param name="legacyEventName">旧事件名称</param>
        /// <param name="legacyEvent">旧UnityEvent</param>
        /// <param name="eventFactory">事件工厂方法</param>
        public void BridgeLegacyToNew<T>(string legacyEventName, UnityEvent legacyEvent, Func<T> eventFactory) where T : IEvent
        {
            legacyEvent.AddListener(() =>
            {
                try
                {
                    var newEvent = eventFactory();
                    _eventBus.Publish(newEvent);
                }
                catch (Exception ex)
                {
                    Debug.LogError($"事件桥接错误 (旧->新): {ex.Message}");
                }
            });

            _legacyEvents[legacyEventName] = legacyEvent;
        }

        /// <summary>
        /// 注册旧UnityEvent<T>到新事件系统的桥接
        /// </summary>
        /// <typeparam name="TLegacy">旧事件参数类型</typeparam>
        /// <typeparam name="TEvent">新事件类型</typeparam>
        /// <param name="legacyEventName">旧事件名称</param>
        /// <param name="legacyEvent">旧UnityEvent<T></param>
        /// <param name="eventFactory">事件工厂方法</param>
        public void BridgeLegacyToNew<TLegacy, TEvent>(string legacyEventName, UnityEvent<TLegacy> legacyEvent, Func<TLegacy, TEvent> eventFactory) where TEvent : IEvent
        {
            legacyEvent.AddListener(legacyData =>
            {
                try
                {
                    var newEvent = eventFactory(legacyData);
                    _eventBus.Publish(newEvent);
                }
                catch (Exception ex)
                {
                    Debug.LogError($"事件桥接错误 (旧->新): {ex.Message}");
                }
            });
            
            // 记录桥接信息
            _legacyEvents[legacyEventName] = legacyEvent;
        }

        #endregion

        #region 双向桥接

        /// <summary>
        /// 建立双向桥接，新旧事件系统可以相互触发
        /// </summary>
        /// <typeparam name="T">新事件类型</typeparam>
        /// <param name="legacyEventName">旧事件名称</param>
        /// <param name="legacyEvent">旧UnityEvent</param>
        /// <param name="newToLegacyConverter">新->旧转换器</param>
        /// <param name="legacyToNewFactory">旧->新工厂方法</param>
        public void BridgeBidirectional<T>(string legacyEventName, UnityEvent legacyEvent, Action<T> newToLegacyConverter, Func<T> legacyToNewFactory) where T : IEvent
        {
            BridgeNewToLegacy(legacyEventName, legacyEvent, newToLegacyConverter);
            BridgeLegacyToNew(legacyEventName, legacyEvent, legacyToNewFactory);
        }

        #endregion

        #region 迁移工具方法

        /// <summary>
        /// 检查组件是否已经迁移到新事件系统
        /// </summary>
        /// <param name="componentName">组件名称</param>
        /// <returns>是否已迁移</returns>
        public bool IsComponentMigrated(string componentName)
        {
            // 检查是否还有该组件的桥接存在
            return !_bridgeSubscriptions.ContainsKey(componentName) || _bridgeSubscriptions[componentName].Count == 0;
        }

        /// <summary>
        /// 标记组件为已迁移
        /// </summary>
        /// <param name="componentName">组件名称</param>
        public void MarkComponentMigrated(string componentName)
        {
            // 这里可以添加迁移状态记录逻辑
            Debug.Log($"组件 {componentName} 已迁移到新事件系统");
        }

        /// <summary>
        /// 移除指定组件的桥接
        /// </summary>
        /// <param name="componentName">组件名称</param>
        public void RemoveBridge(string componentName)
        {
            if (_bridgeSubscriptions.ContainsKey(componentName))
            {
                var subscriptions = _bridgeSubscriptions[componentName];
                foreach (var subscription in subscriptions)
                {
                    subscription?.Dispose();
                }
                _bridgeSubscriptions.Remove(componentName);
            }

            if (_legacyEvents.ContainsKey(componentName))
            {
                _legacyEvents.Remove(componentName);
            }
        }

        /// <summary>
        /// 获取迁移进度报告
        /// </summary>
        /// <returns>迁移进度信息</returns>
        public string GetMigrationReport()
        {
            int totalBridges = _bridgeSubscriptions.Count;
            int activeBridges = 0;
            
            foreach (var kvp in _bridgeSubscriptions)
            {
                if (kvp.Value.Count > 0)
                {
                    activeBridges++;
                }
            }

            return $"迁移进度: {activeBridges}/{totalBridges} 个桥接仍在使用";
        }

        #endregion

        #region 私有方法

        private void AddBridgeSubscription(string bridgeName, IEventSubscription subscription)
        {
            if (!_bridgeSubscriptions.ContainsKey(bridgeName))
            {
                _bridgeSubscriptions[bridgeName] = new List<IEventSubscription>();
            }
            _bridgeSubscriptions[bridgeName].Add(subscription);
        }

        #endregion

        #region 清理

        private void OnDestroy()
        {
            // 清理所有桥接订阅
            foreach (var subscriptions in _bridgeSubscriptions.Values)
            {
                foreach (var subscription in subscriptions)
                {
                    subscription?.Dispose();
                }
            }
            _bridgeSubscriptions.Clear();
            _legacyEvents.Clear();
        }

        #endregion
    }
}