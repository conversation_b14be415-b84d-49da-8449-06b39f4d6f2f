using UnityEngine;
using UnityEngine.Events;
using BlastingDesign.Events.Core;
using BlastingDesign.Events.Migration;

namespace BlastingDesign.Events.Migration.Examples
{
    /// <summary>
    /// Toolbar组件迁移示例
    /// 展示如何将Toolbar从旧的UnityEvent系统迁移到新的EventBus系统
    /// </summary>
    public class ToolbarMigrationExample : MonoBehaviour
    {
        [Header("旧系统 (UnityEvent)")]
        public UnityEvent<string> OnToolSelected_Legacy;
        public UnityEvent<bool> OnToolToggled_Legacy;
        
        [Header("新系统 (EventBus)")]
        private IEventBus _eventBus;
        private EventMigrationHelper _migrationHelper;
        
        [Header("迁移设置")]
        [SerializeField] private bool _useLegacySystem = true;
        [SerializeField] private bool _enableBridging = true;
        [SerializeField] private bool _enableDebugLogs = true;

        private void Start()
        {
            InitializeEventSystems();
            SetupEventBridges();
            SetupEventSubscriptions();
        }

        private void InitializeEventSystems()
        {
            // 初始化新事件系统
            _eventBus = new EventBus(debugMode: true); // 临时创建，实际使用时需要从单例获取
            _migrationHelper = EventMigrationHelper.Instance;
            
            // 初始化旧事件系统
            OnToolSelected_Legacy ??= new UnityEvent<string>();
            OnToolToggled_Legacy ??= new UnityEvent<bool>();
                
            LogDebug("事件系统初始化完成");
        }

        private void SetupEventBridges()
        {
            if (!_enableBridging) return;
            
            // 设置新事件到旧事件的桥接
            _migrationHelper.BridgeNewToLegacy<ToolSelectedEvent, string>(
                "Toolbar_ToolSelected",
                OnToolSelected_Legacy,
                evt => evt.ToolName
            );
            
            _migrationHelper.BridgeNewToLegacy<ToolToggledEvent, bool>(
                "Toolbar_ToolToggled", 
                OnToolToggled_Legacy,
                evt => evt.IsEnabled
            );
            
            // 设置旧事件到新事件的桥接
            _migrationHelper.BridgeLegacyToNew(
                "Toolbar_ToolSelected",
                OnToolSelected_Legacy,
                toolName => new ToolSelectedEvent(toolName)
            );
            
            _migrationHelper.BridgeLegacyToNew(
                "Toolbar_ToolToggled",
                OnToolToggled_Legacy,
                isEnabled => new ToolToggledEvent(isEnabled)
            );
            
            LogDebug("事件桥接设置完成");
        }

        private void SetupEventSubscriptions()
        {
            // 订阅新事件系统
            _eventBus.Subscribe<ToolSelectedEvent>(OnToolSelected_New);
            _eventBus.Subscribe<ToolToggledEvent>(OnToolToggled_New);
            
            // 订阅旧事件系统
            OnToolSelected_Legacy.AddListener(OnToolSelected_Legacy_Handler);
            OnToolToggled_Legacy.AddListener(OnToolToggled_Legacy_Handler);
            
            LogDebug("事件订阅设置完成");
        }

        #region 工具操作方法 (公共接口)

        /// <summary>
        /// 选择工具 - 支持新旧系统切换
        /// </summary>
        /// <param name="toolName">工具名称</param>
        public void SelectTool(string toolName)
        {
            if (_useLegacySystem)
            {
                // 使用旧系统
                OnToolSelected_Legacy?.Invoke(toolName);
                LogDebug($"[Legacy] 选择工具: {toolName}");
            }
            else
            {
                // 使用新系统
                _eventBus.Publish(new ToolSelectedEvent(toolName));
                LogDebug($"[New] 选择工具: {toolName}");
            }
        }

        /// <summary>
        /// 切换工具状态 - 支持新旧系统切换
        /// </summary>
        /// <param name="isEnabled">是否启用</param>
        public void ToggleTool(bool isEnabled)
        {
            if (_useLegacySystem)
            {
                // 使用旧系统
                OnToolToggled_Legacy?.Invoke(isEnabled);
                LogDebug($"[Legacy] 切换工具: {isEnabled}");
            }
            else
            {
                // 使用新系统
                _eventBus.Publish(new ToolToggledEvent(isEnabled));
                LogDebug($"[New] 切换工具: {isEnabled}");
            }
        }

        #endregion

        #region 新事件系统处理器

        private void OnToolSelected_New(ToolSelectedEvent evt)
        {
            LogDebug($"[New Event Handler] 工具选择: {evt.ToolName}");
            
            // 在这里添加新系统的业务逻辑
            // 例如：更新UI状态、保存配置等
            UpdateToolbarUI(evt.ToolName);
        }

        private void OnToolToggled_New(ToolToggledEvent evt)
        {
            LogDebug($"[New Event Handler] 工具切换: {evt.IsEnabled}");
            
            // 在这里添加新系统的业务逻辑
            UpdateToolbarState(evt.IsEnabled);
        }

        #endregion

        #region 旧事件系统处理器

        private void OnToolSelected_Legacy_Handler(string toolName)
        {
            LogDebug($"[Legacy Event Handler] 工具选择: {toolName}");
            
            // 在这里添加旧系统的业务逻辑
            UpdateToolbarUI(toolName);
        }

        private void OnToolToggled_Legacy_Handler(bool isEnabled)
        {
            LogDebug($"[Legacy Event Handler] 工具切换: {isEnabled}");
            
            // 在这里添加旧系统的业务逻辑
            UpdateToolbarState(isEnabled);
        }

        #endregion

        #region 业务逻辑方法

        private void UpdateToolbarUI(string selectedTool)
        {
            // 更新工具栏UI的逻辑
            LogDebug($"更新工具栏UI: 当前选择 {selectedTool}");
        }

        private void UpdateToolbarState(bool isEnabled)
        {
            // 更新工具栏状态的逻辑
            LogDebug($"更新工具栏状态: {(isEnabled ? "启用" : "禁用")}");
        }

        #endregion

        #region 迁移控制方法

        /// <summary>
        /// 切换到新事件系统
        /// </summary>
        public void SwitchToNewSystem()
        {
            _useLegacySystem = false;
            LogDebug("已切换到新事件系统");
        }

        /// <summary>
        /// 切换到旧事件系统
        /// </summary>
        public void SwitchToLegacySystem()
        {
            _useLegacySystem = true;
            LogDebug("已切换到旧事件系统");
        }

        /// <summary>
        /// 完成迁移，移除桥接
        /// </summary>
        public void CompleteMigration()
        {
            // 移除桥接
            _migrationHelper.RemoveBridge("Toolbar_ToolSelected");
            _migrationHelper.RemoveBridge("Toolbar_ToolToggled");
            
            // 取消旧事件订阅
            OnToolSelected_Legacy.RemoveAllListeners();
            OnToolToggled_Legacy.RemoveAllListeners();
            
            // 强制使用新系统
            _useLegacySystem = false;
            _enableBridging = false;
            
            LogDebug("Toolbar迁移完成");
        }

        #endregion

        #region 事件类型定义

        public class ToolSelectedEvent : EventBase
        {
            public string ToolName { get; }
            
            public ToolSelectedEvent(string toolName) : base("Toolbar")
            {
                ToolName = toolName;
            }
        }

        public class ToolToggledEvent : EventBase
        {
            public bool IsEnabled { get; }
            
            public ToolToggledEvent(bool isEnabled) : base("Toolbar")
            {
                IsEnabled = isEnabled;
            }
        }

        #endregion

        #region 测试方法

        [Header("测试按钮")]
        public string TestToolName = "SelectTool";
        public bool TestToggleState = true;

        [ContextMenu("测试工具选择")]
        public void TestToolSelection()
        {
            SelectTool(TestToolName);
        }

        [ContextMenu("测试工具切换")]
        public void TestToolToggle()
        {
            ToggleTool(TestToggleState);
        }

        [ContextMenu("切换事件系统")]
        public void TestSwitchSystem()
        {
            if (_useLegacySystem)
                SwitchToNewSystem();
            else
                SwitchToLegacySystem();
        }

        #endregion

        #region 工具方法

        private void LogDebug(string message)
        {
            if (_enableDebugLogs)
            {
                Debug.Log($"[ToolbarMigration] {message}");
            }
        }

        #endregion

        #region 清理

        private void OnDestroy()
        {
            // 清理事件订阅
            if (_eventBus != null)
            {
                // 新系统的订阅会在EventBus销毁时自动清理
            }
            
            // 清理旧系统订阅
            OnToolSelected_Legacy?.RemoveAllListeners();
            OnToolToggled_Legacy?.RemoveAllListeners();
        }

        #endregion
    }
}