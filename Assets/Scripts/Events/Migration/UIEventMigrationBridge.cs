using System;
using UnityEngine;
using UnityEngine.Events;
using BlastingDesign.Events.Core;

namespace BlastingDesign.Events.Migration
{
    /// <summary>
    /// UI事件迁移桥接器
    /// 专门处理UI相关事件的新旧系统桥接
    /// </summary>
    public class UIEventMigrationBridge : MonoBehaviour
    {
        private EventMigrationHelper _migrationHelper;
        private IEventBus _eventBus;

        [Header("调试设置")]
        [SerializeField] private bool _enableDebugLogs = true;

        private void Awake()
        {
            _migrationHelper = EventMigrationHelper.Instance;
            _eventBus = new EventBus(debugMode: true); // 临时创建，实际使用时需要从单例获取
            
            SetupUIEventBridges();
        }

        private void SetupUIEventBridges()
        {
            // 设置常用UI事件的桥接
            SetupToolbarBridges();
            SetupMenuBarBridges();
            SetupStatusBarBridges();
            SetupLeftPanelBridges();
        }

        #region Toolbar桥接

        private void SetupToolbarBridges()
        {
            // 创建示例的Legacy事件（实际使用时应该从具体组件获取）
            var toolSelectedEvent = new UnityEvent<string>();
            var toolToggledEvent = new UnityEvent<bool>();
            
            // 工具选择事件桥接
            _migrationHelper.BridgeNewToLegacy<ToolSelectedEvent, string>(
                "ToolSelected",
                toolSelectedEvent,
                newEvent => newEvent.ToolName
            );

            // 工具切换事件桥接
            _migrationHelper.BridgeNewToLegacy<ToolToggledEvent, bool>(
                "ToolToggled",
                toolToggledEvent,
                newEvent => newEvent.IsEnabled
            );

            LogBridgeSetup("Toolbar");
        }

        #endregion

        #region MenuBar桥接

        private void SetupMenuBarBridges()
        {
            // 创建示例的Legacy事件
            var menuItemSelectedEvent = new UnityEvent<string>();
            var menuToggleEvent = new UnityEvent<bool>();
            
            // 菜单项选择事件桥接
            _migrationHelper.BridgeNewToLegacy<MenuItemSelectedEvent, string>(
                "MenuItemSelected",
                menuItemSelectedEvent,
                newEvent => newEvent.MenuItemId
            );

            // 菜单展开/收起事件桥接
            _migrationHelper.BridgeNewToLegacy<MenuToggleEvent, bool>(
                "MenuToggled",
                menuToggleEvent,
                newEvent => newEvent.IsExpanded
            );

            LogBridgeSetup("MenuBar");
        }

        #endregion

        #region StatusBar桥接

        private void SetupStatusBarBridges()
        {
            // 创建示例的Legacy事件
            var statusUpdatedEvent = new UnityEvent<string>();
            var progressUpdatedEvent = new UnityEvent<float>();
            
            // 状态更新事件桥接
            _migrationHelper.BridgeNewToLegacy<StatusUpdatedEvent, string>(
                "StatusUpdated",
                statusUpdatedEvent,
                newEvent => newEvent.StatusText
            );

            // 进度更新事件桥接
            _migrationHelper.BridgeNewToLegacy<ProgressUpdatedEvent, float>(
                "ProgressUpdated",
                progressUpdatedEvent,
                newEvent => newEvent.Progress
            );

            LogBridgeSetup("StatusBar");
        }

        #endregion

        #region LeftPanel桥接

        private void SetupLeftPanelBridges()
        {
            // 创建示例的Legacy事件
            var panelStateChangedEvent = new UnityEvent<bool>();
            var panelContentSelectedEvent = new UnityEvent<string>();
            
            // 面板状态变更事件桥接
            _migrationHelper.BridgeNewToLegacy<PanelStateChangedEvent, bool>(
                "PanelStateChanged",
                panelStateChangedEvent,
                newEvent => newEvent.IsExpanded
            );

            // 面板内容选择事件桥接
            _migrationHelper.BridgeNewToLegacy<PanelContentSelectedEvent, string>(
                "PanelContentSelected",
                panelContentSelectedEvent,
                newEvent => newEvent.ContentId
            );

            LogBridgeSetup("LeftPanel");
        }

        #endregion

        #region 通用桥接方法

        /// <summary>
        /// 为指定组件设置双向桥接
        /// </summary>
        /// <param name="componentName">组件名称</param>
        /// <param name="legacyEvent">旧事件</param>
        /// <param name="newEventFactory">新事件工厂</param>
        public void SetupBidirectionalBridge<T>(string componentName, UnityEvent legacyEvent, Func<T> newEventFactory) where T : IEvent
        {
            _migrationHelper.BridgeBidirectional(
                componentName,
                legacyEvent,
                newEvent => { /* 新到旧的转换逻辑 */ },
                newEventFactory
            );

            LogBridgeSetup($"{componentName} (双向)");
        }

        /// <summary>
        /// 移除指定组件的桥接
        /// </summary>
        /// <param name="componentName">组件名称</param>
        public void RemoveBridge(string componentName)
        {
            _migrationHelper.RemoveBridge(componentName);
            LogBridgeRemoval(componentName);
        }

        #endregion

        #region 事件类型定义

        // 这些事件类型需要在实际的事件定义文件中定义
        // 这里仅作为示例展示桥接的使用方式

        public class ToolSelectedEvent : EventBase
        {
            public string ToolName { get; }
            
            public ToolSelectedEvent(string toolName) : base("Toolbar")
            {
                ToolName = toolName;
            }
        }

        public class ToolToggledEvent : EventBase
        {
            public bool IsEnabled { get; }
            
            public ToolToggledEvent(bool isEnabled) : base("Toolbar")
            {
                IsEnabled = isEnabled;
            }
        }

        public class MenuItemSelectedEvent : EventBase
        {
            public string MenuItemId { get; }
            
            public MenuItemSelectedEvent(string menuItemId) : base("MenuBar")
            {
                MenuItemId = menuItemId;
            }
        }

        public class MenuToggleEvent : EventBase
        {
            public bool IsExpanded { get; }
            
            public MenuToggleEvent(bool isExpanded) : base("MenuBar")
            {
                IsExpanded = isExpanded;
            }
        }

        public class StatusUpdatedEvent : EventBase
        {
            public string StatusText { get; }
            
            public StatusUpdatedEvent(string statusText) : base("StatusBar")
            {
                StatusText = statusText;
            }
        }

        public class ProgressUpdatedEvent : EventBase
        {
            public float Progress { get; }
            
            public ProgressUpdatedEvent(float progress) : base("StatusBar")
            {
                Progress = progress;
            }
        }

        public class PanelStateChangedEvent : EventBase
        {
            public bool IsExpanded { get; }
            
            public PanelStateChangedEvent(bool isExpanded) : base("LeftPanel")
            {
                IsExpanded = isExpanded;
            }
        }

        public class PanelContentSelectedEvent : EventBase
        {
            public string ContentId { get; }
            
            public PanelContentSelectedEvent(string contentId) : base("LeftPanel")
            {
                ContentId = contentId;
            }
        }

        #endregion

        #region 调试和日志

        private void LogBridgeSetup(string componentName)
        {
            if (_enableDebugLogs)
            {
                Debug.Log($"[UIEventMigrationBridge] 已设置 {componentName} 的事件桥接");
            }
        }

        private void LogBridgeRemoval(string componentName)
        {
            if (_enableDebugLogs)
            {
                Debug.Log($"[UIEventMigrationBridge] 已移除 {componentName} 的事件桥接");
            }
        }

        #endregion

        #region 生命周期

        private void OnDestroy()
        {
            // 清理工作在EventMigrationHelper中处理
        }

        #endregion
    }
}