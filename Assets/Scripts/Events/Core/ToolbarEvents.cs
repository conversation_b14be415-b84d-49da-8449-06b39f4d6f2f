using UnityEngine;

namespace BlastingDesign.Events.Core
{
    #region Toolbar专用事件

    /// <summary>
    /// 工具按钮点击事件
    /// </summary>
    public class ToolButtonClickedEvent : EventBase
    {
        public string ButtonName { get; set; }
        public string DisplayName { get; set; }
        public bool IsToggle { get; set; }
        public bool IsActive { get; set; }
        public object ButtonData { get; set; }
        public string CallbackName { get; set; }
        public object[] Parameters { get; set; }

        public ToolButtonClickedEvent(string buttonName, string displayName, bool isToggle = false, bool isActive = false, object buttonData = null, string callbackName = null, object[] parameters = null, string source = "Toolbar")
            : base(source)
        {
            ButtonName = buttonName;
            DisplayName = displayName;
            IsToggle = isToggle;
            IsActive = isActive;
            ButtonData = buttonData;
            CallbackName = callbackName;
            Parameters = parameters;
        }

        public override void Reset()
        {
            base.Reset();
            ButtonName = null;
            DisplayName = null;
            IsToggle = false;
            IsActive = false;
            ButtonData = null;
            CallbackName = null;
            Parameters = null;
        }
    }

    /// <summary>
    /// 工具组切换事件
    /// </summary>
    public class ToolGroupSwitchedEvent : EventBase
    {
        public string GroupName { get; set; }
        public string PreviousToolName { get; set; }
        public string CurrentToolName { get; set; }

        public ToolGroupSwitchedEvent(string groupName, string currentToolName, string previousToolName = null, string source = "Toolbar")
            : base(source)
        {
            GroupName = groupName;
            CurrentToolName = currentToolName;
            PreviousToolName = previousToolName;
        }

        public override void Reset()
        {
            base.Reset();
            GroupName = null;
            PreviousToolName = null;
            CurrentToolName = null;
        }
    }

    /// <summary>
    /// 工具栏配置变更事件
    /// </summary>
    public class ToolbarConfigChangedEvent : EventBase
    {
        public string ConfigName { get; set; }
        public object OldConfig { get; set; }
        public object NewConfig { get; set; }

        public ToolbarConfigChangedEvent(string configName, object newConfig, object oldConfig = null, string source = "Toolbar")
            : base(source)
        {
            ConfigName = configName;
            NewConfig = newConfig;
            OldConfig = oldConfig;
        }

        public override void Reset()
        {
            base.Reset();
            ConfigName = null;
            OldConfig = null;
            NewConfig = null;
        }
    }

    /// <summary>
    /// 工具栏布局变更事件
    /// </summary>
    public class ToolbarLayoutChangedEvent : EventBase
    {
        public string LayoutName { get; set; }
        public Vector2 Size { get; set; }
        public bool IsCollapsed { get; set; }

        public ToolbarLayoutChangedEvent(string layoutName, Vector2 size, bool isCollapsed = false, string source = "Toolbar")
            : base(source)
        {
            LayoutName = layoutName;
            Size = size;
            IsCollapsed = isCollapsed;
        }

        public override void Reset()
        {
            base.Reset();
            LayoutName = null;
            Size = Vector2.zero;
            IsCollapsed = false;
        }
    }

    /// <summary>
    /// 工具快捷键触发事件
    /// </summary>
    public class ToolShortcutTriggeredEvent : EventBase
    {
        public string ToolName { get; set; }
        public KeyCode KeyCode { get; set; }
        public bool CtrlPressed { get; set; }
        public bool ShiftPressed { get; set; }
        public bool AltPressed { get; set; }

        public ToolShortcutTriggeredEvent(string toolName, KeyCode keyCode, bool ctrlPressed = false, bool shiftPressed = false, bool altPressed = false, string source = "Toolbar")
            : base(source)
        {
            ToolName = toolName;
            KeyCode = keyCode;
            CtrlPressed = ctrlPressed;
            ShiftPressed = shiftPressed;
            AltPressed = altPressed;
        }

        public override void Reset()
        {
            base.Reset();
            ToolName = null;
            KeyCode = KeyCode.None;
            CtrlPressed = false;
            ShiftPressed = false;
            AltPressed = false;
        }
    }

    /// <summary>
    /// 工具状态变更事件
    /// </summary>
    public class ToolStateChangedEvent : EventBase
    {
        public string ToolName { get; set; }
        public ToolState PreviousState { get; set; }
        public ToolState CurrentState { get; set; }
        public object StateData { get; set; }

        public ToolStateChangedEvent(string toolName, ToolState currentState, ToolState previousState = ToolState.Inactive, object stateData = null, string source = "Toolbar")
            : base(source)
        {
            ToolName = toolName;
            CurrentState = currentState;
            PreviousState = previousState;
            StateData = stateData;
        }

        public override void Reset()
        {
            base.Reset();
            ToolName = null;
            PreviousState = ToolState.Inactive;
            CurrentState = ToolState.Inactive;
            StateData = null;
        }
    }

    /// <summary>
    /// 工具提示显示事件
    /// </summary>
    public class ToolTooltipEvent : EventBase
    {
        public string ToolName { get; set; }
        public string TooltipText { get; set; }
        public Vector2 Position { get; set; }
        public bool IsShowing { get; set; }

        public ToolTooltipEvent(string toolName, string tooltipText, Vector2 position, bool isShowing = true, string source = "Toolbar")
            : base(source)
        {
            ToolName = toolName;
            TooltipText = tooltipText;
            Position = position;
            IsShowing = isShowing;
        }

        public override void Reset()
        {
            base.Reset();
            ToolName = null;
            TooltipText = null;
            Position = Vector2.zero;
            IsShowing = true;
        }
    }

    #endregion

    #region 枚举定义

    /// <summary>
    /// 工具状态枚举
    /// </summary>
    public enum ToolState
    {
        Inactive,
        Active,
        Disabled,
        Hidden
    }

    #endregion
}
