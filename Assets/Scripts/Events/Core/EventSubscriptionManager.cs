using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using UnityEngine;

namespace BlastingDesign.Events.Core
{
    /// <summary>
    /// 线程安全的事件订阅管理器实现
    /// </summary>
    public class EventSubscriptionManager : IEventSubscriptionManager
    {
        private readonly ReaderWriterLockSlim _lock = new ReaderWriterLockSlim();
        private readonly Dictionary<Type, SubscriptionCollection> _subscriptions = new Dictionary<Type, SubscriptionCollection>();
        private readonly bool _debugMode;
        private volatile bool _disposed = false;
        
        // 自动清理定时器
        private Timer _cleanupTimer;
        private readonly TimeSpan _cleanupInterval = TimeSpan.FromMinutes(5);
        
        public EventSubscriptionManager(bool debugMode = false)
        {
            _debugMode = debugMode;
            
            // 启动自动清理定时器
            _cleanupTimer = new Timer(AutoCleanup, null, _cleanupInterval, _cleanupInterval);
        }
        
        public IEventSubscription Subscribe<T>(Action<T> handler) where T : IEvent
        {
            return Subscribe(handler, null, 0);
        }
        
        public IEventSubscription Subscribe<T>(Action<T> handler, Func<T, bool> filter) where T : IEvent
        {
            return Subscribe(handler, filter, 0);
        }
        
        public IEventSubscription Subscribe<T>(Action<T> handler, int priority) where T : IEvent
        {
            return Subscribe(handler, null, priority);
        }
        
        public IEventSubscription Subscribe<T>(Action<T> handler, Func<T, bool> filter, int priority) where T : IEvent
        {
            if (handler == null)
                throw new ArgumentNullException(nameof(handler));
            
            if (_disposed)
                throw new ObjectDisposedException(nameof(EventSubscriptionManager));
            
            var eventType = typeof(T);
            var subscription = new EventSubscription<T>(handler, filter, priority, eventType);
            
            _lock.EnterWriteLock();
            try
            {
                if (!_subscriptions.TryGetValue(eventType, out var collection))
                {
                    collection = new SubscriptionCollection();
                    _subscriptions[eventType] = collection;
                }
                
                collection.Add(subscription);
                
                if (_debugMode)
                {
                    Debug.Log($"EventSubscriptionManager: 订阅事件 {eventType.Name}，优先级: {priority}，总订阅数: {collection.Count}");
                }
            }
            finally
            {
                _lock.ExitWriteLock();
            }
            
            return subscription;
        }
        
        public void Unsubscribe<T>(Action<T> handler) where T : IEvent
        {
            if (handler == null || _disposed) return;
            
            var eventType = typeof(T);
            
            _lock.EnterWriteLock();
            try
            {
                if (_subscriptions.TryGetValue(eventType, out var collection))
                {
                    var removed = collection.Remove(handler);
                    
                    if (removed > 0 && _debugMode)
                    {
                        Debug.Log($"EventSubscriptionManager: 取消订阅事件 {eventType.Name}，移除数量: {removed}");
                    }
                    
                    // 如果集合为空，移除整个集合
                    if (collection.Count == 0)
                    {
                        _subscriptions.Remove(eventType);
                    }
                }
            }
            finally
            {
                _lock.ExitWriteLock();
            }
        }
        
        public void Unsubscribe(IEventSubscription subscription)
        {
            if (subscription == null || !subscription.IsValid || _disposed) return;
            
            _lock.EnterWriteLock();
            try
            {
                if (_subscriptions.TryGetValue(subscription.EventType, out var collection))
                {
                    var removed = collection.Remove(subscription);
                    
                    if (removed && _debugMode)
                    {
                        Debug.Log($"EventSubscriptionManager: 通过句柄取消订阅事件 {subscription.EventType.Name}");
                    }
                    
                    // 如果集合为空，移除整个集合
                    if (collection.Count == 0)
                    {
                        _subscriptions.Remove(subscription.EventType);
                    }
                }
            }
            finally
            {
                _lock.ExitWriteLock();
            }
            
            subscription.Dispose();
        }
        
        public IEnumerable<IEventSubscription> GetSubscriptions<T>() where T : IEvent
        {
            var eventType = typeof(T);
            
            _lock.EnterReadLock();
            try
            {
                if (_subscriptions.TryGetValue(eventType, out var collection))
                {
                    return collection.GetValidSubscriptions().ToList(); // 返回副本
                }
                return Enumerable.Empty<IEventSubscription>();
            }
            finally
            {
                _lock.ExitReadLock();
            }
        }
        
        public int GetSubscriberCount<T>() where T : IEvent
        {
            var eventType = typeof(T);
            
            _lock.EnterReadLock();
            try
            {
                if (_subscriptions.TryGetValue(eventType, out var collection))
                {
                    return collection.GetValidCount();
                }
                return 0;
            }
            finally
            {
                _lock.ExitReadLock();
            }
        }
        
        public bool HasSubscribers<T>() where T : IEvent
        {
            return GetSubscriberCount<T>() > 0;
        }
        
        public void ClearAllSubscriptions()
        {
            _lock.EnterWriteLock();
            try
            {
                int totalCleared = 0;
                foreach (var collection in _subscriptions.Values)
                {
                    totalCleared += collection.Count;
                    collection.Clear();
                }
                
                _subscriptions.Clear();
                
                if (_debugMode)
                {
                    Debug.Log($"EventSubscriptionManager: 清除所有订阅，总数: {totalCleared}");
                }
            }
            finally
            {
                _lock.ExitWriteLock();
            }
        }
        
        public void ClearSubscriptions<T>() where T : IEvent
        {
            var eventType = typeof(T);
            
            _lock.EnterWriteLock();
            try
            {
                if (_subscriptions.TryGetValue(eventType, out var collection))
                {
                    int count = collection.Count;
                    collection.Clear();
                    _subscriptions.Remove(eventType);
                    
                    if (_debugMode)
                    {
                        Debug.Log($"EventSubscriptionManager: 清除事件 {eventType.Name} 的所有订阅，数量: {count}");
                    }
                }
            }
            finally
            {
                _lock.ExitWriteLock();
            }
        }
        
        public int CleanupInvalidSubscriptions()
        {
            int totalCleaned = 0;
            
            _lock.EnterWriteLock();
            try
            {
                var keysToRemove = new List<Type>();
                
                foreach (var kvp in _subscriptions)
                {
                    int cleaned = kvp.Value.CleanupInvalid();
                    totalCleaned += cleaned;
                    
                    if (kvp.Value.Count == 0)
                    {
                        keysToRemove.Add(kvp.Key);
                    }
                }
                
                foreach (var key in keysToRemove)
                {
                    _subscriptions.Remove(key);
                }
                
                if (_debugMode && totalCleaned > 0)
                {
                    Debug.Log($"EventSubscriptionManager: 清理失效订阅，数量: {totalCleaned}");
                }
            }
            finally
            {
                _lock.ExitWriteLock();
            }
            
            return totalCleaned;
        }
        
        private void AutoCleanup(object state)
        {
            if (!_disposed)
            {
                CleanupInvalidSubscriptions();
            }
        }
        
        public void Dispose()
        {
            if (_disposed) return;
            
            _disposed = true;
            
            _cleanupTimer?.Dispose();
            _cleanupTimer = null;
            
            ClearAllSubscriptions();
            
            _lock.Dispose();
        }
    }
    
    /// <summary>
    /// 订阅集合，支持优先级排序和高效查找
    /// </summary>
    internal class SubscriptionCollection
    {
        private readonly List<IEventSubscription> _subscriptions = new List<IEventSubscription>();
        private readonly HashSet<IEventSubscription> _subscriptionSet = new HashSet<IEventSubscription>();
        private bool _sorted = true;
        
        public int Count => _subscriptions.Count;
        
        public void Add(IEventSubscription subscription)
        {
            if (_subscriptionSet.Add(subscription))
            {
                _subscriptions.Add(subscription);
                _sorted = false;
            }
        }
        
        public int Remove<T>(Action<T> handler) where T : IEvent
        {
            int removed = 0;
            
            for (int i = _subscriptions.Count - 1; i >= 0; i--)
            {
                var subscription = _subscriptions[i];
                if (subscription is EventSubscription<T> typedSubscription &&
                    typedSubscription.Handler.Equals(handler))
                {
                    _subscriptions.RemoveAt(i);
                    _subscriptionSet.Remove(subscription);
                    subscription.Dispose();
                    removed++;
                }
            }
            
            return removed;
        }
        
        public bool Remove(IEventSubscription subscription)
        {
            if (_subscriptionSet.Remove(subscription))
            {
                _subscriptions.Remove(subscription);
                return true;
            }
            return false;
        }
        
        public IEnumerable<IEventSubscription> GetValidSubscriptions()
        {
            EnsureSorted();
            return _subscriptions.Where(s => s.IsValid);
        }
        
        public int GetValidCount()
        {
            return _subscriptions.Count(s => s.IsValid);
        }
        
        public int CleanupInvalid()
        {
            int cleaned = 0;
            
            for (int i = _subscriptions.Count - 1; i >= 0; i--)
            {
                var subscription = _subscriptions[i];
                if (!subscription.IsValid)
                {
                    _subscriptions.RemoveAt(i);
                    _subscriptionSet.Remove(subscription);
                    subscription.Dispose();
                    cleaned++;
                }
            }
            
            return cleaned;
        }
        
        public void Clear()
        {
            foreach (var subscription in _subscriptions)
            {
                subscription.Dispose();
            }
            
            _subscriptions.Clear();
            _subscriptionSet.Clear();
            _sorted = true;
        }
        
        private void EnsureSorted()
        {
            if (!_sorted)
            {
                _subscriptions.Sort((a, b) => b.Priority.CompareTo(a.Priority)); // 高优先级在前
                _sorted = true;
            }
        }
    }
}