using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace BlastingDesign.Events.Core
{
    /// <summary>
    /// 事件发布器接口
    /// 负责事件的发布和分发
    /// </summary>
    public interface IEventPublisher : IDisposable
    {
        /// <summary>
        /// 同步发布事件
        /// </summary>
        /// <typeparam name="T">事件类型</typeparam>
        /// <param name="eventData">事件数据</param>
        /// <returns>事件发布结果</returns>
        EventPublishResult Publish<T>(T eventData) where T : IEvent;
        
        /// <summary>
        /// 异步发布事件
        /// </summary>
        /// <typeparam name="T">事件类型</typeparam>
        /// <param name="eventData">事件数据</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>事件发布结果</returns>
        Task<EventPublishResult> PublishAsync<T>(T eventData, CancellationToken cancellationToken = default) where T : IEvent;
        
        /// <summary>
        /// 延迟发布事件
        /// </summary>
        /// <typeparam name="T">事件类型</typeparam>
        /// <param name="eventData">事件数据</param>
        /// <param name="delay">延迟时间</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>延迟发布任务</returns>
        Task<EventPublishResult> PublishDelayed<T>(T eventData, TimeSpan delay, CancellationToken cancellationToken = default) where T : IEvent;
        
        /// <summary>
        /// 批量发布事件
        /// </summary>
        /// <typeparam name="T">事件类型</typeparam>
        /// <param name="events">事件集合</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>批量发布结果</returns>
        Task<IEnumerable<EventPublishResult>> PublishBatch<T>(IEnumerable<T> events, CancellationToken cancellationToken = default) where T : IEvent;
        
        /// <summary>
        /// 启用/禁用调试模式
        /// </summary>
        bool DebugMode { get; set; }
        
        /// <summary>
        /// 获取发布统计信息
        /// </summary>
        /// <returns>发布统计</returns>
        IEventPublishStatistics GetPublishStatistics();
    }
    
    /// <summary>
    /// 事件发布结果
    /// </summary>
    public class EventPublishResult
    {
        /// <summary>
        /// 事件ID
        /// </summary>
        public string EventId { get; set; }
        
        /// <summary>
        /// 事件类型
        /// </summary>
        public Type EventType { get; set; }
        
        /// <summary>
        /// 是否发布成功
        /// </summary>
        public bool IsSuccess { get; set; }
        
        /// <summary>
        /// 处理的订阅者数量
        /// </summary>
        public int ProcessedSubscribers { get; set; }
        
        /// <summary>
        /// 处理时间（毫秒）
        /// </summary>
        public double ProcessingTime { get; set; }
        
        /// <summary>
        /// 异常信息（如果有）
        /// </summary>
        public Exception Exception { get; set; }
        
        /// <summary>
        /// 是否被取消
        /// </summary>
        public bool IsCancelled { get; set; }
        
        /// <summary>
        /// 发布时间
        /// </summary>
        public DateTime PublishTime { get; set; }
    }
    
    /// <summary>
    /// 事件发布统计信息接口
    /// </summary>
    public interface IEventPublishStatistics
    {
        /// <summary>
        /// 总发布事件数
        /// </summary>
        long TotalPublishedEvents { get; }
        
        /// <summary>
        /// 成功发布事件数
        /// </summary>
        long SuccessfulPublishedEvents { get; }
        
        /// <summary>
        /// 失败发布事件数
        /// </summary>
        long FailedPublishedEvents { get; }
        
        /// <summary>
        /// 平均处理时间（毫秒）
        /// </summary>
        double AverageProcessingTime { get; }
        
        /// <summary>
        /// 最大处理时间（毫秒）
        /// </summary>
        double MaxProcessingTime { get; }
        
        /// <summary>
        /// 最小处理时间（毫秒）
        /// </summary>
        double MinProcessingTime { get; }
        
        /// <summary>
        /// 获取指定事件类型的发布统计
        /// </summary>
        /// <param name="eventType">事件类型</param>
        /// <returns>事件类型发布统计</returns>
        IEventTypePublishStatistics GetEventTypeStatistics(Type eventType);
    }
    
    /// <summary>
    /// 事件类型发布统计信息接口
    /// </summary>
    public interface IEventTypePublishStatistics
    {
        /// <summary>
        /// 事件类型
        /// </summary>
        Type EventType { get; }
        
        /// <summary>
        /// 发布次数
        /// </summary>
        long PublishCount { get; }
        
        /// <summary>
        /// 成功发布次数
        /// </summary>
        long SuccessfulPublishCount { get; }
        
        /// <summary>
        /// 失败发布次数
        /// </summary>
        long FailedPublishCount { get; }
        
        /// <summary>
        /// 平均处理时间（毫秒）
        /// </summary>
        double AverageProcessingTime { get; }
        
        /// <summary>
        /// 最后发布时间
        /// </summary>
        DateTime LastPublishTime { get; }
    }
}