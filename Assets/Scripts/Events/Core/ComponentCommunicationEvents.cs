using System;
using UnityEngine;

namespace BlastingDesign.Events.Core
{
    #region 组件生命周期事件

    /// <summary>
    /// 组件初始化事件
    /// </summary>
    public class ComponentInitializedEvent : EventBase
    {
        public string ComponentName { get; set; }
        public Type ComponentType { get; set; }
        public object ComponentInstance { get; set; }

        public ComponentInitializedEvent(string componentName, Type componentType, object componentInstance = null, string source = "ComponentManager")
            : base(source)
        {
            ComponentName = componentName;
            ComponentType = componentType;
            ComponentInstance = componentInstance;
        }

        public override void Reset()
        {
            base.Reset();
            ComponentName = null;
            ComponentType = null;
            ComponentInstance = null;
        }
    }

    /// <summary>
    /// 组件销毁事件
    /// </summary>
    public class ComponentDestroyedEvent : EventBase
    {
        public string ComponentName { get; set; }
        public Type ComponentType { get; set; }

        public ComponentDestroyedEvent(string componentName, Type componentType, string source = "ComponentManager")
            : base(source)
        {
            ComponentName = componentName;
            ComponentType = componentType;
        }

        public override void Reset()
        {
            base.Reset();
            ComponentName = null;
            ComponentType = null;
        }
    }

    #endregion

    #region 数据通信事件

    /// <summary>
    /// 数据更新事件
    /// </summary>
    public class DataUpdatedEvent : EventBase
    {
        public string DataKey { get; set; }
        public object OldValue { get; set; }
        public object NewValue { get; set; }
        public string UpdateSource { get; set; }

        public DataUpdatedEvent(string dataKey, object newValue, object oldValue = null, string updateSource = null, string source = "DataManager")
            : base(source)
        {
            DataKey = dataKey;
            NewValue = newValue;
            OldValue = oldValue;
            UpdateSource = updateSource;
        }

        public override void Reset()
        {
            base.Reset();
            DataKey = null;
            OldValue = null;
            NewValue = null;
            UpdateSource = null;
        }
    }

    /// <summary>
    /// 数据请求事件
    /// </summary>
    public class DataRequestEvent : EventBase
    {
        public string DataKey { get; set; }
        public string RequesterId { get; set; }
        public object RequestParameters { get; set; }

        public DataRequestEvent(string dataKey, string requesterId, object requestParameters = null, string source = "DataManager")
            : base(source)
        {
            DataKey = dataKey;
            RequesterId = requesterId;
            RequestParameters = requestParameters;
        }

        public override void Reset()
        {
            base.Reset();
            DataKey = null;
            RequesterId = null;
            RequestParameters = null;
        }
    }

    /// <summary>
    /// 数据响应事件
    /// </summary>
    public class DataResponseEvent : EventBase
    {
        public string DataKey { get; set; }
        public string RequesterId { get; set; }
        public object ResponseData { get; set; }
        public bool IsSuccess { get; set; }
        public string ErrorMessage { get; set; }

        public DataResponseEvent(string dataKey, string requesterId, object responseData, bool isSuccess = true, string errorMessage = null, string source = "DataManager")
            : base(source)
        {
            DataKey = dataKey;
            RequesterId = requesterId;
            ResponseData = responseData;
            IsSuccess = isSuccess;
            ErrorMessage = errorMessage;
        }

        public override void Reset()
        {
            base.Reset();
            DataKey = null;
            RequesterId = null;
            ResponseData = null;
            IsSuccess = true;
            ErrorMessage = null;
        }
    }

    #endregion

    #region 状态同步事件

    /// <summary>
    /// 状态变更事件
    /// </summary>
    public class StateChangedEvent : EventBase
    {
        public string StateName { get; set; }
        public object PreviousState { get; set; }
        public object CurrentState { get; set; }
        public string StateOwner { get; set; }

        public StateChangedEvent(string stateName, object currentState, object previousState = null, string stateOwner = null, string source = "StateManager")
            : base(source)
        {
            StateName = stateName;
            CurrentState = currentState;
            PreviousState = previousState;
            StateOwner = stateOwner;
        }

        public override void Reset()
        {
            base.Reset();
            StateName = null;
            PreviousState = null;
            CurrentState = null;
            StateOwner = null;
        }
    }

    /// <summary>
    /// 状态同步请求事件
    /// </summary>
    public class StateSyncRequestEvent : EventBase
    {
        public string StateName { get; set; }
        public string RequesterId { get; set; }

        public StateSyncRequestEvent(string stateName, string requesterId, string source = "StateManager")
            : base(source)
        {
            StateName = stateName;
            RequesterId = requesterId;
        }

        public override void Reset()
        {
            base.Reset();
            StateName = null;
            RequesterId = null;
        }
    }

    #endregion

    #region 通知事件

    /// <summary>
    /// 通知事件
    /// </summary>
    public class NotificationEvent : EventBase
    {
        public string Title { get; set; }
        public string Message { get; set; }
        public NotificationType Type { get; set; }
        public float Duration { get; set; }
        public object Data { get; set; }

        public NotificationEvent(string title, string message, NotificationType type = NotificationType.Info, float duration = 3f, object data = null, string source = "NotificationManager")
            : base(source)
        {
            Title = title;
            Message = message;
            Type = type;
            Duration = duration;
            Data = data;
        }

        public override void Reset()
        {
            base.Reset();
            Title = null;
            Message = null;
            Type = NotificationType.Info;
            Duration = 3f;
            Data = null;
        }
    }

    /// <summary>
    /// 确认对话框事件
    /// </summary>
    public class ConfirmationDialogEvent : EventBase
    {
        public string Title { get; set; }
        public string Message { get; set; }
        public string ConfirmText { get; set; }
        public string CancelText { get; set; }
        public Action<bool> Callback { get; set; }

        public ConfirmationDialogEvent(string title, string message, Action<bool> callback, string confirmText = "确认", string cancelText = "取消", string source = "DialogManager")
            : base(source)
        {
            Title = title;
            Message = message;
            Callback = callback;
            ConfirmText = confirmText;
            CancelText = cancelText;
        }

        public override void Reset()
        {
            base.Reset();
            Title = null;
            Message = null;
            ConfirmText = "确认";
            CancelText = "取消";
            Callback = null;
        }
    }

    #endregion

    #region 工作流事件

    /// <summary>
    /// 任务开始事件
    /// </summary>
    public class TaskStartedEvent : EventBase
    {
        public string TaskId { get; set; }
        public string TaskName { get; set; }
        public object TaskData { get; set; }

        public TaskStartedEvent(string taskId, string taskName, object taskData = null, string source = "TaskManager")
            : base(source)
        {
            TaskId = taskId;
            TaskName = taskName;
            TaskData = taskData;
        }

        public override void Reset()
        {
            base.Reset();
            TaskId = null;
            TaskName = null;
            TaskData = null;
        }
    }

    /// <summary>
    /// 任务完成事件
    /// </summary>
    public class TaskCompletedEvent : EventBase
    {
        public string TaskId { get; set; }
        public string TaskName { get; set; }
        public bool IsSuccess { get; set; }
        public object Result { get; set; }
        public string ErrorMessage { get; set; }

        public TaskCompletedEvent(string taskId, string taskName, bool isSuccess = true, object result = null, string errorMessage = null, string source = "TaskManager")
            : base(source)
        {
            TaskId = taskId;
            TaskName = taskName;
            IsSuccess = isSuccess;
            Result = result;
            ErrorMessage = errorMessage;
        }

        public override void Reset()
        {
            base.Reset();
            TaskId = null;
            TaskName = null;
            IsSuccess = true;
            Result = null;
            ErrorMessage = null;
        }
    }

    /// <summary>
    /// 进度更新事件
    /// </summary>
    public class ProgressUpdatedEvent : EventBase
    {
        public string TaskId { get; set; }
        public float Progress { get; set; }
        public string StatusMessage { get; set; }

        public ProgressUpdatedEvent(string taskId, float progress, string statusMessage = null, string source = "TaskManager")
            : base(source)
        {
            TaskId = taskId;
            Progress = Mathf.Clamp01(progress);
            StatusMessage = statusMessage;
        }

        public override void Reset()
        {
            base.Reset();
            TaskId = null;
            Progress = 0f;
            StatusMessage = null;
        }
    }

    #endregion

    #region 枚举定义

    /// <summary>
    /// 通知类型
    /// </summary>
    public enum NotificationType
    {
        Info,
        Success,
        Warning,
        Error
    }

    #endregion
}
