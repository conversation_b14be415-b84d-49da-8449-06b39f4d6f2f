using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using UnityEngine;
using BlastingDesign.Events.Services;

namespace BlastingDesign.Events.Core
{
    /// <summary>
    /// 重构后的事件总线实现
    /// 使用组合模式，职责更加单一
    /// </summary>
    public class EventBus : IEventBus
    {
        private readonly IEventSubscriptionManager _subscriptionManager;
        private readonly IEventPublisher _publisher;
        private readonly IEventErrorHandler _errorHandler;
        private readonly EventPool _eventPool;
        private readonly EventScheduler _scheduler;
        private readonly EventPerformanceMonitor _performanceMonitor;
        
        // 配置
        public bool DebugMode { get; set; }
        private readonly bool _useObjectPool;
        private readonly bool _enablePerformanceMonitoring;
        private volatile bool _disposed = false;

        public EventBus(bool useObjectPool = true, bool debugMode = false, bool enablePerformanceMonitoring = true)
        {
            _useObjectPool = useObjectPool;
            _enablePerformanceMonitoring = enablePerformanceMonitoring;
            DebugMode = debugMode;

            // 创建组件
            _subscriptionManager = new EventSubscriptionManager(debugMode);
            _errorHandler = new EventErrorHandler(debugMode);
            _publisher = new EventPublisher(_subscriptionManager, _errorHandler, debugMode);

            if (_useObjectPool)
            {
                _eventPool = new EventPool(100, debugMode);
            }

            _scheduler = new EventScheduler(0.016f, debugMode);

            if (_enablePerformanceMonitoring)
            {
                _performanceMonitor = new EventPerformanceMonitor(1000, debugMode);
            }
        }
        
        /// <summary>
        /// 带依赖注入的构造函数
        /// </summary>
        public EventBus(
            IEventSubscriptionManager subscriptionManager,
            IEventPublisher publisher,
            IEventErrorHandler errorHandler = null,
            EventPool eventPool = null,
            EventScheduler scheduler = null,
            EventPerformanceMonitor performanceMonitor = null)
        {
            _subscriptionManager = subscriptionManager ?? throw new ArgumentNullException(nameof(subscriptionManager));
            _publisher = publisher ?? throw new ArgumentNullException(nameof(publisher));
            _errorHandler = errorHandler;
            _eventPool = eventPool;
            _scheduler = scheduler;
            _performanceMonitor = performanceMonitor;
            
            _useObjectPool = _eventPool != null;
            _enablePerformanceMonitoring = _performanceMonitor != null;
        }

        #region 基础订阅/取消订阅

        public IEventSubscription Subscribe<T>(Action<T> handler) where T : IEvent
        {
            if (_disposed) throw new ObjectDisposedException(nameof(EventBus));
            return _subscriptionManager.Subscribe(handler);
        }

        public void Unsubscribe<T>(Action<T> handler) where T : IEvent
        {
            if (_disposed) return;
            _subscriptionManager.Unsubscribe(handler);
        }

        public void Unsubscribe(IEventSubscription subscription)
        {
            if (_disposed) return;
            _subscriptionManager.Unsubscribe(subscription);
        }

        #endregion

        #region 条件订阅

        public IEventSubscription Subscribe<T>(Action<T> handler, Func<T, bool> filter) where T : IEvent
        {
            if (_disposed) throw new ObjectDisposedException(nameof(EventBus));
            return _subscriptionManager.Subscribe(handler, filter);
        }

        #endregion

        #region 优先级订阅

        public IEventSubscription Subscribe<T>(Action<T> handler, int priority) where T : IEvent
        {
            if (_disposed) throw new ObjectDisposedException(nameof(EventBus));
            return _subscriptionManager.Subscribe(handler, priority);
        }

        public IEventSubscription Subscribe<T>(Action<T> handler, Func<T, bool> filter, int priority) where T : IEvent
        {
            if (_disposed) throw new ObjectDisposedException(nameof(EventBus));
            return _subscriptionManager.Subscribe(handler, filter, priority);
        }

        #endregion

        #region 事件发布

        public void Publish<T>(T eventData) where T : IEvent
        {
            if (_disposed) throw new ObjectDisposedException(nameof(EventBus));
            
            var result = _publisher.Publish(eventData);
            
            // 性能监控
            if (_enablePerformanceMonitoring && _performanceMonitor != null)
            {
                _performanceMonitor.RecordEventExecution(
                    result.EventType, 
                    result.ProcessingTime, 
                    result.ProcessedSubscribers, 
                    eventData.Source);
            }

            // 如果使用对象池，将事件返回到池中
            if (_useObjectPool && _eventPool != null)
            {
                _eventPool.Return(eventData);
            }
        }

        public async Task PublishAsync<T>(T eventData) where T : IEvent
        {
            if (_disposed) throw new ObjectDisposedException(nameof(EventBus));
            
            var result = await _publisher.PublishAsync(eventData);
            
            // 性能监控
            if (_enablePerformanceMonitoring && _performanceMonitor != null)
            {
                _performanceMonitor.RecordEventExecution(
                    result.EventType, 
                    result.ProcessingTime, 
                    result.ProcessedSubscribers, 
                    eventData.Source);
            }

            // 如果使用对象池，将事件返回到池中
            if (_useObjectPool && _eventPool != null)
            {
                _eventPool.Return(eventData);
            }
        }

        #endregion

        #region 延迟和批量发布

        public void PublishDelayed<T>(T eventData, float delay) where T : IEvent
        {
            if (_disposed) throw new ObjectDisposedException(nameof(EventBus));
            
            if (_scheduler != null)
            {
                _scheduler.ScheduleEvent(eventData, delay, Publish);
            }
            else
            {
                // 如果没有调度器，使用发布器的延迟功能
                _ = _publisher.PublishDelayed(eventData, TimeSpan.FromSeconds(delay));
            }
        }

        public void PublishBatch<T>(IEnumerable<T> events) where T : IEvent
        {
            if (_disposed) throw new ObjectDisposedException(nameof(EventBus));
            
            if (_scheduler != null)
            {
                _scheduler.ScheduleBatch(events, Publish);
            }
            else
            {
                // 如果没有调度器，使用发布器的批量功能
                _ = _publisher.PublishBatch(events);
            }
        }

        #endregion

        #region 查询和管理

        public int GetSubscriberCount<T>() where T : IEvent
        {
            if (_disposed) return 0;
            return _subscriptionManager.GetSubscriberCount<T>();
        }

        public bool HasSubscribers<T>() where T : IEvent
        {
            if (_disposed) return false;
            return _subscriptionManager.HasSubscribers<T>();
        }

        public void ClearAllSubscriptions()
        {
            if (_disposed) return;
            _subscriptionManager.ClearAllSubscriptions();
        }

        public void ClearSubscriptions<T>() where T : IEvent
        {
            if (_disposed) return;
            _subscriptionManager.ClearSubscriptions<T>();
        }

        #endregion

        #region 事件统计和调试

        public IEventStatistics GetStatistics()
        {
            if (_disposed) return null;
            return _publisher?.GetPublishStatistics() as IEventStatistics;
        }

        #endregion

        #region 性能监控

        /// <summary>
        /// 获取性能监控器
        /// </summary>
        /// <returns>性能监控器实例</returns>
        public EventPerformanceMonitor GetPerformanceMonitor()
        {
            return _performanceMonitor;
        }

        /// <summary>
        /// 生成性能报告
        /// </summary>
        /// <returns>性能报告字符串</returns>
        public string GeneratePerformanceReport()
        {
            return _performanceMonitor?.GeneratePerformanceReport() ?? "性能监控未启用";
        }

        /// <summary>
        /// 重置性能统计
        /// </summary>
        public void ResetPerformanceStatistics()
        {
            _performanceMonitor?.Reset();
        }

        #endregion

        #region 错误处理

        /// <summary>
        /// 注册错误恢复策略
        /// </summary>
        /// <typeparam name="T">事件类型</typeparam>
        /// <param name="strategy">恢复策略</param>
        public void RegisterErrorRecoveryStrategy<T>(IEventErrorRecoveryStrategy<T> strategy) where T : IEvent
        {
            if (_disposed) return;
            _errorHandler?.RegisterRecoveryStrategy(strategy);
        }

        /// <summary>
        /// 移除错误恢复策略
        /// </summary>
        /// <typeparam name="T">事件类型</typeparam>
        public void RemoveErrorRecoveryStrategy<T>() where T : IEvent
        {
            if (_disposed) return;
            _errorHandler?.RemoveRecoveryStrategy<T>();
        }

        /// <summary>
        /// 获取错误统计信息
        /// </summary>
        /// <returns>错误统计</returns>
        public IEventErrorStatistics GetErrorStatistics()
        {
            if (_disposed) return null;
            return _errorHandler?.GetErrorStatistics();
        }

        #endregion

        /// <summary>
        /// 处理调度的事件
        /// 应该在主线程的Update中调用
        /// </summary>
        public void Update()
        {
            if (_disposed) return;
            _scheduler?.ProcessScheduledEvents();
        }

        public void Dispose()
        {
            if (_disposed) return;
            
            _disposed = true;
            
            _subscriptionManager?.Dispose();
            _publisher?.Dispose();
            _eventPool?.Dispose();
            _scheduler?.Dispose();
            _performanceMonitor?.Dispose();
        }
    }

}
