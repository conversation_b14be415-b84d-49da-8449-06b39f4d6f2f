using System;
using System.Threading.Tasks;

namespace BlastingDesign.Events.Core
{
    /// <summary>
    /// 事件错误处理器接口
    /// 负责统一的错误处理和恢复策略
    /// </summary>
    public interface IEventErrorHandler
    {
        /// <summary>
        /// 处理事件错误
        /// </summary>
        /// <typeparam name="T">事件类型</typeparam>
        /// <param name="eventData">事件数据</param>
        /// <param name="exception">异常</param>
        /// <param name="context">错误上下文</param>
        /// <returns>错误处理结果</returns>
        Task<EventErrorResult> HandleError<T>(T eventData, Exception exception, EventErrorContext context) where T : IEvent;
        
        /// <summary>
        /// 注册错误恢复策略
        /// </summary>
        /// <typeparam name="T">事件类型</typeparam>
        /// <param name="strategy">恢复策略</param>
        void RegisterRecoveryStrategy<T>(IEventErrorRecoveryStrategy<T> strategy) where T : IEvent;
        
        /// <summary>
        /// 移除错误恢复策略
        /// </summary>
        /// <typeparam name="T">事件类型</typeparam>
        void RemoveRecoveryStrategy<T>() where T : IEvent;
        
        /// <summary>
        /// 获取错误统计信息
        /// </summary>
        /// <returns>错误统计</returns>
        IEventErrorStatistics GetErrorStatistics();
    }
    
    /// <summary>
    /// 事件错误恢复策略接口
    /// </summary>
    /// <typeparam name="T">事件类型</typeparam>
    public interface IEventErrorRecoveryStrategy<T> where T : IEvent
    {
        /// <summary>
        /// 尝试恢复
        /// </summary>
        /// <param name="eventData">事件数据</param>
        /// <param name="exception">异常</param>
        /// <param name="context">错误上下文</param>
        /// <returns>恢复结果</returns>
        Task<EventErrorRecoveryResult> TryRecover(T eventData, Exception exception, EventErrorContext context);
    }
    
    /// <summary>
    /// 事件错误上下文
    /// </summary>
    public class EventErrorContext
    {
        /// <summary>
        /// 错误阶段
        /// </summary>
        public EventErrorStage Stage { get; set; }
        
        /// <summary>
        /// 重试次数
        /// </summary>
        public int RetryCount { get; set; }
        
        /// <summary>
        /// 最大重试次数
        /// </summary>
        public int MaxRetries { get; set; }
        
        /// <summary>
        /// 订阅者信息
        /// </summary>
        public IEventSubscription Subscription { get; set; }
        
        /// <summary>
        /// 错误时间
        /// </summary>
        public DateTime ErrorTime { get; set; }
        
        /// <summary>
        /// 附加信息
        /// </summary>
        public object AdditionalInfo { get; set; }
    }
    
    /// <summary>
    /// 事件错误阶段
    /// </summary>
    public enum EventErrorStage
    {
        /// <summary>
        /// 事件发布前
        /// </summary>
        BeforePublish,
        
        /// <summary>
        /// 事件处理中
        /// </summary>
        DuringProcessing,
        
        /// <summary>
        /// 事件发布后
        /// </summary>
        AfterPublish,
        
        /// <summary>
        /// 订阅管理
        /// </summary>
        SubscriptionManagement
    }
    
    /// <summary>
    /// 事件错误结果
    /// </summary>
    public class EventErrorResult
    {
        /// <summary>
        /// 是否已处理
        /// </summary>
        public bool IsHandled { get; set; }
        
        /// <summary>
        /// 是否应该重试
        /// </summary>
        public bool ShouldRetry { get; set; }
        
        /// <summary>
        /// 重试延迟时间
        /// </summary>
        public TimeSpan RetryDelay { get; set; }
        
        /// <summary>
        /// 是否应该停止进一步处理
        /// </summary>
        public bool ShouldStopProcessing { get; set; }
        
        /// <summary>
        /// 错误描述
        /// </summary>
        public string ErrorDescription { get; set; }
        
        /// <summary>
        /// 恢复动作
        /// </summary>
        public Func<Task> RecoveryAction { get; set; }
    }
    
    /// <summary>
    /// 事件错误恢复结果
    /// </summary>
    public class EventErrorRecoveryResult
    {
        /// <summary>
        /// 是否恢复成功
        /// </summary>
        public bool IsRecovered { get; set; }
        
        /// <summary>
        /// 恢复描述
        /// </summary>
        public string RecoveryDescription { get; set; }
        
        /// <summary>
        /// 是否需要重新处理事件
        /// </summary>
        public bool ShouldRetryEvent { get; set; }
        
        /// <summary>
        /// 重试延迟时间
        /// </summary>
        public TimeSpan RetryDelay { get; set; }
    }
    
    /// <summary>
    /// 事件错误统计信息接口
    /// </summary>
    public interface IEventErrorStatistics
    {
        /// <summary>
        /// 总错误数
        /// </summary>
        long TotalErrors { get; }
        
        /// <summary>
        /// 已处理错误数
        /// </summary>
        long HandledErrors { get; }
        
        /// <summary>
        /// 未处理错误数
        /// </summary>
        long UnhandledErrors { get; }
        
        /// <summary>
        /// 恢复成功数
        /// </summary>
        long RecoveredErrors { get; }
        
        /// <summary>
        /// 错误率
        /// </summary>
        double ErrorRate { get; }
        
        /// <summary>
        /// 恢复率
        /// </summary>
        double RecoveryRate { get; }
        
        /// <summary>
        /// 获取指定事件类型的错误统计
        /// </summary>
        /// <param name="eventType">事件类型</param>
        /// <returns>事件类型错误统计</returns>
        IEventTypeErrorStatistics GetEventTypeErrorStatistics(Type eventType);
    }
    
    /// <summary>
    /// 事件类型错误统计信息接口
    /// </summary>
    public interface IEventTypeErrorStatistics
    {
        /// <summary>
        /// 事件类型
        /// </summary>
        Type EventType { get; }
        
        /// <summary>
        /// 错误次数
        /// </summary>
        long ErrorCount { get; }
        
        /// <summary>
        /// 恢复次数
        /// </summary>
        long RecoveryCount { get; }
        
        /// <summary>
        /// 最后错误时间
        /// </summary>
        DateTime LastErrorTime { get; }
        
        /// <summary>
        /// 常见错误类型
        /// </summary>
        string[] CommonErrorTypes { get; }
    }
}